import 'package:flutter/material.dart';
import 'package:myrewards/componentes/widget_tienda_info.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:myrewards/dtos/campacna.dart';
import 'package:myrewards/estados/state_tarjetas.dart';
import 'package:myrewards/estados/state_tiendas_destacadas.dart';
import 'package:myrewards/servicios/exception/api_exception.dart';
import 'package:myrewards/servicios/service_tarjeta.dart';
import 'package:myrewards/servicios/service_tienda.dart';
import 'package:provider/provider.dart';

class TiendaInfoDialog extends StatefulWidget {
  final String tiendaCodigo;
  final bool mostrarAgregar;
  const TiendaInfoDialog(
      {super.key, required this.tiendaCodigo, this.mostrarAgregar = true});

  @override
  State<TiendaInfoDialog> createState() => _TiendaInfoDialogState();
}

class _TiendaInfoDialogState extends State<TiendaInfoDialog> {
  Campacna? campacna;
  bool agregando = false;

  void buscarCampacna() {
    TiendaApi().buscarCampacnaTienda(widget.tiendaCodigo).then((value) {
      setState(() {
        campacna = value;
      });
    });
  }

  @override
  void initState() {
    super.initState();
    buscarCampacna();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      elevation: 0,
      backgroundColor: ColoresApp.fondo,
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            child: campacna == null
                ? const Center(child: CircularProgressIndicator())
                : Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(height: 24),
                      Text(campacna!.nombreTienda,
                          style: const TextStyle(fontSize: 14)),
                      const SizedBox(height: 4),
                      Text(campacna!.nombreCampacna,
                          style: const TextStyle(
                              fontSize: 21,
                              fontWeight: FontWeight.bold,
                              color: ColoresApp.acento)),
                      Expanded(child: TiendaInfoWidget(campacna: campacna!)),
                      const SizedBox(height: 8),
                      Visibility(
                        visible: widget.mostrarAgregar,
                        child: SizedBox(
                          height: 55,
                          width: double.infinity,
                          child: ElevatedButton(
                              onPressed: () {
                                if (agregando) return;
                                setState(() {
                                  agregando = true;
                                });
                                TarjetaApi()
                                    .asociarTarjeta(widget.tiendaCodigo)
                                    .then((tarjeta) {
                                  setState(() {
                                    agregando = false;
                                  });
                                  if (tarjeta != null) {
                                    context
                                        .read<TiendasDestacadasState>()
                                        .actualizarDestacados();
                                    context
                                        .read<TarjetasState>()
                                        .agregarTarjeta(tarjeta);

                                    Navigator.of(context).pop("A");
                                  }
                                }, onError: (e) {
                                  String errorMessage;

                                  if (e is ApiException) {
                                    errorMessage = e.message;
                                  } else {
                                    errorMessage = e.toString();
                                  }

                                  ScaffoldMessenger.of(context)
                                      .showSnackBar(SnackBar(
                                    content: Text(errorMessage),
                                  ));
                                });
                              },
                              style: ElevatedButton.styleFrom(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20),
                                ),
                              ),
                              child: agregando
                                  ? const Center(
                                      child: CircularProgressIndicator())
                                  : const Text("Agregar",
                                      style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold))),
                        ),
                      ),
                      const SizedBox(height: 8)
                    ],
                  ),
          ),
          Positioned(
            top: 0,
            right: 0,
            child: IconButton(
                iconSize: 30,
                icon: const Icon(
                  Icons.cancel,
                  color: Colors.grey,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                }),
          )
        ],
      ),
    );
  }
}
