class Tarjeta {
  final int id;
  final String tiendaNombre;
  final String tituloRecompensa;
  final String condicionCorta;
  final String codigo;
  final DateTime recompensaValidaHasta;
  final String tiendaLogo;
  final String tiendaCodigo;
  final String tiendaCategoria;
  int hitsUsuario;
  int hits;

  Tarjeta(
      {required this.id,
      required this.tiendaNombre,
      required this.tituloRecompensa,
      required this.condicionCorta,
      required this.codigo,
      required this.recompensaValidaHasta,
      required this.tiendaLogo,
      required this.tiendaCodigo,
      required this.tiendaCategoria,
      required this.hitsUsuario,
      required this.hits});

  static Tarjeta fromJson(Map<String, dynamic> json) => Tarjeta(
        id: 1,
        tiendaNombre: json['shoppingName'],
        tituloRecompensa: json['rewardTitle'],
        condicionCorta: json['shortCondition'],
        codigo: json['cardCode'],
        tiendaCategoria: json['shoppingCategory'],
        recompensaValidaHasta: DateTime.parse(json['rewardEndDate']),
        tiendaLogo: json['shoppingImage'],
        tiendaCodigo: json['shoppingCode'],
        hitsUsuario: json['userHits'],
        hits: json['hits'],
      );
}
