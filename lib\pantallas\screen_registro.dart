import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:myrewards/componentes/text_form_field_app.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:myrewards/dtos/usuario.dart';
import 'package:myrewards/servicios/exception/api_exception.dart';
import 'package:myrewards/servicios/service_autorizacion.dart';
import 'package:myrewards/storage/secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

class RegistroScreen extends StatefulWidget {
  const RegistroScreen({super.key});

  @override
  State<RegistroScreen> createState() => _RegistroScreenState();
}

class _RegistroScreenState extends State<RegistroScreen> {
  final SecureStorage secureStorage = SecureStorage();
  final TextEditingController tecNombres = TextEditingController();
  final TextEditingController tecCorreo = TextEditingController();
  final TextEditingController tecClave = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool agregando = false;

  void continuar() {
    if (agregando) return;
    if (_formKey.currentState!.validate()) {
      setState(() {
        agregando = true;
      });
      final usuario = Usuario(
          nombres: tecNombres.text,
          correo: tecCorreo.text,
          clave: tecClave.text);
      AuthApi().registrar(usuario).then((token) {
        debugPrint('token generado $token');
        secureStorage
            .writeSecureData("token", token)
            .then((value) => {context.go('/inicio')});
      }, onError: (e) {
        String errorMessage;

        if (e is ApiException) {
          errorMessage = e.message;
        } else {
          errorMessage = e.toString();
        }

        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(errorMessage),
        ));

        setState(() {
          agregando = false;
        });
      });
    }
  }

  @override
  void initState() {
    super.initState();
    marcarPrimeraVez();
  }

  marcarPrimeraVez() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('primeraVez', false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          backgroundColor: Colors.transparent,
          foregroundColor: ColoresApp.texto,
          elevation: 0,
          title: const Text(
            "Registrate",
            style: TextStyle(fontWeight: FontWeight.bold),
          )),
      body: SingleChildScrollView(
        child: Padding(
          padding:
              const EdgeInsets.only(top: 21, left: 16, right: 16, bottom: 8),
          child: Form(
            key: _formKey,
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  TextFormFieldApp(
                      controller: tecNombres,
                      textInputAction: TextInputAction.next,
                      label: "Nombres",
                      hint: "Escribe tu nombre completo acá",
                      validatior: (value) {
                        if (value == null || value.isEmpty) {
                          return "Debe ingresar su nombre completo";
                        }
                        return null;
                      }),
                  const SizedBox(height: 8),
                  TextFormFieldApp(
                      controller: tecCorreo,
                      textInputAction: TextInputAction.next,
                      keyboardType: TextInputType.emailAddress,
                      label: "Correo electrónico",
                      hint: "Escribe tu correo acá",
                      validatior: (value) {
                        if (value == null || value.isEmpty) {
                          return "Debe ingresar el correo electrónico";
                        }

                        bool emailValid = RegExp(
                                r"^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,253}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,253}[a-zA-Z0-9])?)*$")
                            .hasMatch(value);

                        if (!emailValid) {
                          return 'Correo electrónico inválido';
                        }
                        return null;
                      }),
                  const SizedBox(height: 8),
                  TextFormFieldApp(
                      controller: tecClave,
                      label: "Clave",
                      hint: "Escribe tu clave",
                      password: true,
                      textInputAction: TextInputAction.done,
                      validatior: (value) {
                        if (value == null || value.isEmpty) {
                          return "Debe ingresar su clave";
                        }
                        return null;
                      },
                      onFieldSubmitted: (_) => continuar()),
                  const SizedBox(height: 16),
                  SizedBox(
                      width: double.infinity,
                      height: 55,
                      child: ElevatedButton(
                          onPressed: continuar,
                          style: ElevatedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                          child: agregando
                              ? const Center(child: CircularProgressIndicator())
                              : const Text(
                                  "Registrar",
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold),
                                ))),
                ]),
          ),
        ),
      ),
    );
  }
}
