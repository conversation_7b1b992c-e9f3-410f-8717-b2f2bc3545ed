import 'package:flutter/material.dart';
import 'package:myrewards/componentes/dialog_tienda_info.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:myrewards/pantallas/inicio/widgets/widget_card_destacado.dart';
import 'package:myrewards/estados/state_tiendas_destacadas.dart';
import 'package:myrewards/servicios/service_tienda.dart';
import 'package:myrewards/dtos/tienda_destacada.dart';
import 'package:myrewards/dtos/comunes/paginado.dart';
import 'package:provider/provider.dart';
import 'dart:developer';

class VerMasScreen extends StatefulWidget {
  const VerMasScreen({super.key});

  @override
  State<VerMasScreen> createState() => _VerMasScreenState();
}

class _VerMasScreenState extends State<VerMasScreen> {
  final ScrollController _scrollController = ScrollController();
  List<TiendaDestacada> _tiendas = [];
  int _currentPage = 1;
  final int _pageSize = 10;
  bool _isLoading = false;
  bool _hasMoreData = true;

  @override
  void initState() {
    super.initState();
    _loadMoreData();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent * 0.8 &&
        !_isLoading &&
        _hasMoreData) {
      _loadMoreData();
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    final state = Provider.of<TiendasDestacadasState>(context, listen: false);
    final latitud = state.latitud;
    final longitud = state.longitud;

    try {
      final Paginado paginado = await TiendaApi().tiendasDestacadasGpsPaginadas(
          latitud, longitud, _currentPage, _pageSize);

      final List<TiendaDestacada> newTiendas =
          paginado.content.map((e) => TiendaDestacada.fromJson(e)).toList();

      setState(() {
        _tiendas.addAll(newTiendas);
        _currentPage++;
        _isLoading = false;
        _hasMoreData = !paginado.last && newTiendas.isNotEmpty;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshData() async {
    setState(() {
      _tiendas = [];
      _currentPage = 1;
      _hasMoreData = true;
    });
    await _loadMoreData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        foregroundColor: ColoresApp.texto,
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text("Seleccionar Tarjeta",
            style: TextStyle(fontWeight: FontWeight.bold)),
      ),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: _tiendas.isEmpty && _isLoading
            ? const Center(child: CircularProgressIndicator())
            : ListView.builder(
                controller: _scrollController,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                itemCount: _tiendas.length + (_hasMoreData ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index == _tiendas.length) {
                    return const Center(
                      child: Padding(
                        padding: EdgeInsets.all(8.0),
                        child: CircularProgressIndicator(),
                      ),
                    );
                  }

                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: GestureDetector(
                      onTap: () {
                        showDialog<String>(
                            context: context,
                            builder: (context) {
                              return TiendaInfoDialog(
                                tiendaCodigo: _tiendas[index].codigo,
                              );
                            }).then((aceptado) {
                              log("aceptado: $aceptado");
                              if (aceptado == 'A') {Navigator.of(context).pop();}
                            });
                      },
                      child: CardDestacadoWidget(
                        tiendaDestacada: _tiendas[index],
                      ),
                    ),
                  );
                },
              ),
      ),
    );
  }
}
