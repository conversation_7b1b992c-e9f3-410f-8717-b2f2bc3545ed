import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:myrewards/servicios/service_tienda.dart';

class TiendaMapaWidget extends StatefulWidget {
  final int tiendaId;
  const TiendaMapaWidget({super.key, required this.tiendaId});

  @override
  State<TiendaMapaWidget> createState() => _TiendaMapaWidgetState();
}

class _TiendaMapaWidgetState extends State<TiendaMapaWidget> {
  String? direccion;
  LatLng? coordenadas;

  @override
  void initState() {
    super.initState();
    _cargarUbicacion();
  }

  Future<void> _cargarUbicacion() async {
    try {
      final ubicacion =
          await TiendaApi().buscarUbicacionTienda(widget.tiendaId);
      setState(() {
        direccion = ubicacion['address'];
        coordenadas = LatLng(ubicacion['latitude'], ubicacion['longitude']);
      });
    } catch (e) {
      setState(() {
        direccion = "Error al obtener la ubicación";
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Ubicación',
          style: TextStyle(
            fontSize: 18.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8.0),
        if (direccion != null)
          Text(
            direccion!,
            style: const TextStyle(fontSize: 12.0, color: Colors.grey),
          )
        else
          const CircularProgressIndicator(),
        const SizedBox(height: 16.0),
        if (coordenadas != null)
          ClipRRect(
            borderRadius: BorderRadius.circular(8.0),
            child: SizedBox(
              height: 210.0,
              child: GoogleMap(
                zoomControlsEnabled: false,
                initialCameraPosition: CameraPosition(
                  target: coordenadas!,
                  zoom: 16.0,
                ),
                markers: {
                  Marker(
                    markerId: const MarkerId('tienda'),
                    position: coordenadas!,
                  ),
                },
              ),
            ),
          )
        else if (direccion != null)
          const Text('Cargando mapa...', style: TextStyle(color: Colors.grey)),
      ],
    );
  }
}
