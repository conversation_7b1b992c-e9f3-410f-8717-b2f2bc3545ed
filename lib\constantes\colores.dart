import 'package:flutter/material.dart';

class ColoresApp {
  static const MaterialColor primario =
      MaterialColor(_primarioPrimaryValue, <int, Color>{
    50: Color(0xFFF1F7FF),
    100: Color(0xFFDDEAFF),
    200: Color(0xFFC7DCFF),
    300: Color(0xFFB0CEFE),
    400: Color(0xFF9FC4FE),
    500: Color(_primarioPrimaryValue),
    600: Color(0xFF86B2FE),
    700: Color(0xFF7BAAFE),
    800: Color(0xFF71A2FE),
    900: Color(0xFF5F93FD),
  });
  static const int _primarioPrimaryValue = 0xFF8EB9FE;

  static const MaterialColor acento = MaterialColor(_mcAcento, <int, Color>{
    50: Color(0xFFEAE8F5),
    100: Color(0xFFCAC7E7),
    200: Color(0xFFA7A1D7),
    300: Color(0xFF847BC6),
    400: Color(0xFF695FBA),
    500: Color(_mcAcento),
    600: Color(0xFF483DA7),
    700: Color(0xFF3F349D),
    800: Color(0xFF362C94),
    900: Color(0xFF261E84),
  });
  static const int _mcAcento = 0xFF4F43AE;

  static const MaterialColor segundario =
      MaterialColor(_mcSegundario, <int, Color>{
    50: Color(0xFFFEFEFF),
    100: Color(0xFFFCFCFE),
    200: Color(0xFFFAFAFE),
    300: Color(0xFFF7F8FD),
    400: Color(0xFFF6F7FC),
    500: Color(_mcSegundario),
    600: Color(0xFFF3F4FC),
    700: Color(0xFFF1F2FB),
    800: Color(0xFFEFF0FB),
    900: Color(0xFFECEEFA),
  });
  static const int _mcSegundario = 0xFFF4F5FC;

  static const MaterialColor texto = MaterialColor(_mcTexto, <int, Color>{
    50: Color(0xFFE6E7EB),
    100: Color(0xFFC1C3CC),
    200: Color(0xFF979CAA),
    300: Color(0xFF6D7488),
    400: Color(0xFF4E566F),
    500: Color(_mcTexto),
    600: Color(0xFF2A324E),
    700: Color(0xFF232B44),
    800: Color(0xFF1D243B),
    900: Color(0xFF12172A),
  });
  static const int _mcTexto = 0xFF2F3855;

  static const MaterialColor fondo = MaterialColor(_mcFondo, <int, Color>{
    50: Color(0xFFFEFEFF),
    100: Color(0xFFFCFCFD),
    200: Color(0xFFF9FAFB),
    300: Color(0xFFF6F7F9), // Color principal (intensidad 300)
    400: Color(0xFFF2F4F6),
    500: Color(_mcFondo), // Color principal
    600: Color(0xFFE1E3E6),
    700: Color(0xFFCED1D6),
    800: Color(0xFFBCC0C6),
    900: Color(0xFF9DA1A9),
  });
  static const int _mcFondo = 0xFFF6F7F9;

  static const MaterialColor amarillo = MaterialColor(_mcAmarillo, <int, Color>{
    50: Color(0xFFFFF7E0),
    100: Color(0xFFFFECB3),
    200: Color(0xFFFFE080),
    300: Color(0xFFFFD34D),
    400: Color(0xFFFFC926),
    500: Color(_mcAmarillo), // Color principal
    600: Color(0xFFE6B506),
    700: Color(0xFFCC9F05),
    800: Color(0xFFB38A05),
    900: Color(0xFF806204),
  });
  static const int _mcAmarillo = 0xFFFFC906; // Color #FFC906

  static const MaterialColor verdeBarra =
      MaterialColor(_mcVerdeBarra, <int, Color>{
    50: Color(0xFFE5FDF0), // Un tono más claro
    100: Color(0xFFBDFBE0), // Un tono más claro
    200: Color(0xFF91F8CD), // Un tono más claro
    300: Color(0xFF65F5BA), // Un tono más claro
    400: Color(0xFF45F3AC), // Un tono más claro
    500: Color(_mcVerdeBarra), // Color base (#3DE892)
    600: Color(0xFF37E58A), // Un tono más oscuro
    700: Color(0xFF30E180), // Un tono más oscuro
    800: Color(0xFF28DD76), // Un tono más oscuro
    900: Color(0xFF1BD667), // Un tono más oscuro
  });
  static const int _mcVerdeBarra = 0xFF3DE892; // Color base en hexadecimal
}
