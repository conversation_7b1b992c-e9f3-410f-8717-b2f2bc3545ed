import 'package:flutter/material.dart';
import 'package:myrewards/constantes/colores.dart';

class AlertaDialog extends StatefulWidget {
  final String mensaje;
  const AlertaDialog({super.key, required this.mensaje});

  @override
  State<AlertaDialog> createState() => _AlertaDialogState();
}

class _AlertaDialogState extends State<AlertaDialog> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        elevation: 0,
        backgroundColor: ColoresApp.fondo,
        child: Stack(children: [
          Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: Column(mainAxisSize: MainAxisSize.min, children: [
                const SizedBox(height: 25),
                Text(
                  widget.mensaje,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 24, color: ColoresApp.texto),
                ),
                const SizedBox(height: 24),
                SizedB<PERSON>(
                  height: 55,
                  width: double.infinity,
                  child: SizedBox(
                      height: 55,
                      child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          style: ElevatedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                          child: const Text("Aceptar",
                              style: TextStyle(
                                  fontSize: 16, fontWeight: FontWeight.bold)))),
                ),
                const SizedBox(height: 8)
              ]))
        ]));
  }
}
