import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:myrewards/componentes/dialog_confirmacion.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:myrewards/dtos/cupon_detalle.dart';
import 'package:myrewards/estados/state_cupones.dart';
import 'package:myrewards/servicios/exception/api_exception.dart';
import 'package:myrewards/servicios/service_cupones.dart';
import 'package:myrewards/util/cargando_util.dart';
import 'package:myrewards/util/fecha_util.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:provider/provider.dart';

class DetalleCuponScreen extends StatefulWidget {
  final int id;
  const DetalleCuponScreen({super.key, required this.id});

  @override
  State<DetalleCuponScreen> createState() => _DetalleCuponScreenState();
}

class _DetalleCuponScreenState extends State<DetalleCuponScreen> {
  CuponDetalle? cuponDetalle;

  buscarCuponDetalle() async {
    cuponDetalle = await CuponesApi().buscarCupon(widget.id);
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    buscarCuponDetalle();
  }

  eliminarCupon() {
    CargandoUtil().cargando("Eliminando cupón ...", context);
    CuponesApi().borrarCupon(widget.id).then((valor) {
      if (mounted) {
        Navigator.of(context).pop();
        context.read<CuponesState>().quitarCupon(widget.id);
        Navigator.of(context).pop();
        context.read<CuponesState>().misCupones();
      }
    }, onError: (e) {
      if (mounted) {
        Navigator.of(context).pop();
        String errorMessage;

        if (e is ApiException) {
          errorMessage = e.message;
        } else {
          errorMessage = e.toString();
        }

        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(errorMessage),
        ));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          foregroundColor: ColoresApp.texto,
          backgroundColor: Colors.transparent,
          elevation: 0,
          title: cuponDetalle == null
              ? null
              : Text(cuponDetalle!.tiendaNombre,
                  style: const TextStyle(fontWeight: FontWeight.bold)),
          actions: [
            if (cuponDetalle != null)
              IconButton(
                  onPressed: () {
                    showDialog<String>(
                        context: context,
                        builder: (context) {
                          return const ConfirmacionDialog(
                              mensaje: "¿Está seguro de eliminar el cupón?");
                        }).then((value) {
                      if (value == 'T') {
                        eliminarCupon();
                      }
                    });
                  },
                  icon: SvgPicture.asset('assets/images/basura.svg',
                      height: 20, width: 20))
          ]),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: cuponDetalle == null
                ? const Center(child: CircularProgressIndicator())
                : SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 8, horizontal: 16),
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 8),
                            const Text("Recompensa",
                                style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: ColoresApp.texto)),
                            const SizedBox(height: 4),
                            Text(
                              cuponDetalle!.titulo,
                              style: const TextStyle(color: ColoresApp.texto),
                            ),
                            const SizedBox(height: 16),
                            const Text("Recompensa válida hasta",
                                style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: ColoresApp.texto)),
                            const SizedBox(height: 4),
                            Text(
                              "${DateFormat('dd-MM-yyyy').format(cuponDetalle!.fechaVigencia)} (quedan ${FechaUtil().calcularDias(cuponDetalle!.fechaVigencia)} días)",
                              style: const TextStyle(color: ColoresApp.texto),
                            ),
                            const SizedBox(height: 16),
                            const Text("Condición de uso",
                                style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: ColoresApp.texto)),
                            const SizedBox(height: 4),
                            Text(
                              cuponDetalle!.recompensaUso,
                              style: const TextStyle(color: ColoresApp.texto),
                            )
                          ]),
                    ),
                  ),
          ),
          Container(
            height: 200,
            decoration: const BoxDecoration(
                color: ColoresApp.acento,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(40),
                    topRight: Radius.circular(40))),
            child: cuponDetalle == null
                ? null
                : Center(
                    child: Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      QrImageView(
                        data: cuponDetalle!.codigo,
                        version: QrVersions.auto,
                        eyeStyle: const QrEyeStyle(
                          eyeShape: QrEyeShape.square,
                          color: Colors.white,
                        ),
                        dataModuleStyle: const QrDataModuleStyle(
                          dataModuleShape: QrDataModuleShape.square,
                          color: Colors.white
                        ),
                        size: 120.0,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        cuponDetalle!.codigo,
                        style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            letterSpacing: 5),
                      )
                    ],
                  )),
          ),
        ],
      ),
    );
  }
}
