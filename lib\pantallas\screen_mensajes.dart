import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:myrewards/estados/state_mensajes.dart';
import 'package:myrewards/servicios/service_mensaje.dart';
import 'package:provider/provider.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:go_router/go_router.dart';

class MensajesScreen extends StatefulWidget {
  const MensajesScreen({super.key});

  @override
  State<MensajesScreen> createState() => _MensajesScreenState();
}

class _MensajesScreenState extends State<MensajesScreen> {
  //List<Mensaje> mensajes = [];
  //bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _cargarMensajes();
  }

  Future<void> _cargarMensajes() async {
    context.read<MensajesState>().misMensajes();
  }

  Future<void> _eliminarMensaje(int id) async {
    try {
      await MensajeApi().eliminarMensaje(id);
      mostrarMensaje('Mensaje eliminado');
    } catch (e) {
      mostrarMensaje('Error al eliminar el mensaje');
    }
  }

  void mostrarMensaje(String mensaje) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(mensaje)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mensajes'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: context.watch<MensajesState>().cargando
          ? const Center(child: CircularProgressIndicator())
          : context.watch<MensajesState>().mensajes.isEmpty
              ? Center(
                  child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        const SizedBox(height: 21),
                        SvgPicture.asset('assets/images/sin_cupones.svg'),
                        const SizedBox(height: 16),
                        const Text("Todo tranquilo por aquí, sin mensajes",
                            style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: ColoresApp.acento)),
                      ]),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16.0),
                  itemCount: context.watch<MensajesState>().mensajes.length,
                  itemBuilder: (context, index) {
                    final mensaje =
                        context.watch<MensajesState>().mensajes[index];
                    final backgroundColor = mensaje.estado == 'VIEWED'
                        ? ColoresApp.fondo
                        : Colors.white;

                    return Slidable(
                      key: ValueKey(mensaje.id),
                      endActionPane: ActionPane(
                        motion: const ScrollMotion(),
                        children: [
                          SlidableAction(
                            onPressed: (context) {
                              context
                                  .read<MensajesState>()
                                  .quitarMensaje(mensaje.id);
                              _eliminarMensaje(mensaje.id);
                            },
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            icon: Icons.delete,
                            label: 'Eliminar',
                          ),
                        ],
                      ),
                      child: GestureDetector(
                        onTap: () {
                          context.push(
                            '/mensaje/detalle',
                            extra: mensaje,
                          );
                        },
                        child: Card(
                          elevation: context
                                      .watch<MensajesState>()
                                      .mensajes[index]
                                      .estado ==
                                  'VIEWED'
                              ? 1
                              : 4,
                          color: backgroundColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16.0),
                          ),
                          margin: const EdgeInsets.only(bottom: 16.0),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                CircleAvatar(
                                  backgroundImage:
                                      NetworkImage(mensaje.tiendaImg!),
                                  radius: 30,
                                ),
                                const SizedBox(width: 16.0),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        DateFormat('dd-MM-yyyy')
                                            .format(mensaje.fecha),
                                        style: TextStyle(
                                          fontSize: 12.0,
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                      const SizedBox(height: 4.0),
                                      Text(
                                        mensaje.titulo,
                                        style: const TextStyle(
                                          fontSize: 16.0,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 4.0),
                                      Text(
                                        mensaje.mensaje,
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: const TextStyle(fontSize: 14.0),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
    );
  }
}
