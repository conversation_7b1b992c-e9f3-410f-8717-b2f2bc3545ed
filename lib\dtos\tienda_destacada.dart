class TiendaDestacada {
  final String nombre;
  final String codigo;
  final String imagen;
  final String imagenRecompensa;
  final String recompensa;
  final int estampillas;

  const TiendaDestacada(
      {required this.nombre,
      required this.codigo,
      required this.imagen,
      required this.imagenRecompensa,
      required this.recompensa,
      required this.estampillas});

  static TiendaDestacada fromJson(Map<String, dynamic> json) => TiendaDestacada(
      nombre: json['name'],
      codigo: json['code'],
      imagen: json['image'],
      imagenRecompensa: json['rewardImage'],
      recompensa: json['reward'],
      estampillas: json['cardStamps']);
}
