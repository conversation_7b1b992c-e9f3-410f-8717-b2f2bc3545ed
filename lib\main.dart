import 'package:flutter/material.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:myrewards/estados/state_cupones.dart';
import 'package:myrewards/estados/state_mensajes.dart';
import 'package:myrewards/estados/state_tarjeta.dart';
import 'package:myrewards/estados/state_tarjetas.dart';
import 'package:myrewards/estados/state_tiendas_destacadas.dart';
import 'package:myrewards/estados/state_usuario.dart';
import 'package:myrewards/rutas/rutas.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';
import 'firebase_options.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);
  runApp(MultiProvider(providers: [
    ChangeNotifierProvider(create: (_) => CuponesState()),
    ChangeNotifierProvider(create: (_) => TarjetasState()),
    ChangeNotifierProvider(create: (_) => TiendasDestacadasState()),
    ChangeNotifierProvider(create: (_) => TarjetaState()),
    ChangeNotifierProvider(create: (_) => UsuarioState()),
    ChangeNotifierProvider(create: (_) => MensajesState()),
  ], child: const MyApp()));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    AppRouter.instance.setRouter(AppRouter.initRouter());

    return MaterialApp.router(
      title: 'My Rewards',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        fontFamily: 'Manrope',
        primarySwatch: ColoresApp.acento,
        scaffoldBackgroundColor: ColoresApp.fondo,
      ),
      routerConfig: AppRouter.instance.router,
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('es', 'ES'),
        Locale('en', 'US'),
      ],
    );
  }
}
