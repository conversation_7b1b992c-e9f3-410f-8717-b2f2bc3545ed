import 'package:http_interceptor/http_interceptor.dart';
import 'package:myrewards/storage/secure_storage.dart';

class LoggingInterceptor implements InterceptorContract {
  final SecureStorage secureStorage = SecureStorage();

  @override
  Future<RequestData> interceptRequest({required RequestData data}) async {
    String token = await secureStorage.readSecureData("token") ?? "";
    data.headers["Content-Type"] = "application/json; charset=UTF-8";
    data.headers["Authorization"] = "Bearer $token";
    return data;
  }

  @override
  Future<ResponseData> interceptResponse({required ResponseData data}) async {
    return data;
  }
}
