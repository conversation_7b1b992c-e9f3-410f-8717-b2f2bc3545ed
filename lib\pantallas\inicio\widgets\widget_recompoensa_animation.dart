import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class RewardLottieAnimation extends StatefulWidget {
  const RewardLottieAnimation({
    super.key,
    required this.duration,
  });

  final Duration duration;

  @override
  State<RewardLottieAnimation> createState() => _RewardLottieAnimationState();
}

class _RewardLottieAnimationState extends State<RewardLottieAnimation>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scale = const AlwaysStoppedAnimation(1.0);
  late Animation<double> _opacity = const AlwaysStoppedAnimation(0.0);
  late Animation<Offset> _position = const AlwaysStoppedAnimation(Offset.zero);
  Offset? _centerOffset;
  Offset? _targetOffset;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: widget.duration);
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final screenSize = MediaQuery.of(context).size;
      _centerOffset = Offset(screenSize.width / 2, screenSize.height / 2);
      // Calcular la posición del botón de recompensas (tercer ítem en el BottomNavigationBar)
      _targetOffset = Offset(
        screenSize.width * 0.75, // 75% del ancho (tercer ítem)
        screenSize.height - 28, // Altura del BottomNavigationBar menos un poco para el ícono
      );
      
      _setupAnimations();
      _controller.forward();
    });
  }

  void _setupAnimations() {
    assert(_centerOffset != null, 'El centro debe estar inicializado');
    assert(_targetOffset != null, 'El objetivo debe estar inicializado');

    // Secuencia de escala: aparece grande, se mantiene y luego se hace pequeño
    _scale = TweenSequence<double>([
      TweenSequenceItem(tween: Tween(begin: 0.0, end: 1.2), weight: 20),
      TweenSequenceItem(tween: Tween(begin: 1.2, end: 1.2), weight: 60),
      TweenSequenceItem(tween: Tween(begin: 1.2, end: 0.3), weight: 20),
    ]).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    // Secuencia de posición: comienza en el centro y se mueve al final
    _position = TweenSequence<Offset>([
      TweenSequenceItem(
        tween: Tween(begin: _centerOffset!, end: _centerOffset!),
        weight: 70,
      ),
      TweenSequenceItem(
        tween: Tween(begin: _centerOffset!, end: _targetOffset!)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 30,
      ),
    ]).animate(_controller);

    // Secuencia de opacidad: aparece, se mantiene visible y desaparece
    _opacity = TweenSequence<double>([
      TweenSequenceItem(tween: Tween(begin: 0.0, end: 1.0), weight: 10),
      TweenSequenceItem(tween: Tween(begin: 1.0, end: 1.0), weight: 70),
      TweenSequenceItem(tween: Tween(begin: 1.0, end: 0.0), weight: 20),
    ]).animate(_controller);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Positioned(
          left: _position.value.dx - 150, // Centrar el Lottie
          top: _position.value.dy - 150,  // Centrar el Lottie
          child: Transform.scale(
            scale: _scale.value,
            child: Opacity(
              opacity: _opacity.value,
              child: Lottie.asset(
                'assets/images/reward_star.json',
                width: 300,
                height: 300,
                controller: _controller,
                fit: BoxFit.contain,
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
