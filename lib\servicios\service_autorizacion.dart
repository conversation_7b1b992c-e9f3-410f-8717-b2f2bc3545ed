import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:myrewards/servicios/exception/api_exception.dart';
import 'package:myrewards/servicios/service_usuario.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:http/http.dart' as http;
import 'package:myrewards/constantes/ambiente.dart';
import 'package:myrewards/dtos/usuario.dart';
import 'dart:convert';

import 'package:myrewards/storage/secure_storage.dart';

class AuthApi {
  static final AuthApi _singleton = AuthApi._internal();

  factory AuthApi() {
    return _singleton;
  }

  AuthApi._internal();

  Future<String> registrar(Usuario usuario) async {
    try {
      final response = await http.post(Uri.parse('${Ambiente.urlAuth}/signup'),
          headers: <String, String>{'Content-Type': 'application/json'},
          body: jsonEncode(usuario));

      if (response.statusCode == 200) {
        final SecureStorage secureStorage = SecureStorage();

        final data = jsonDecode(utf8.decode(response.bodyBytes));
        secureStorage.writeSecureData("refreshToken", data['refreshToken']);
        secureStorage.writeSecureData("user", usuario.correo);
        secureStorage.writeSecureData("loginType", "app");
        return data['accessToken'];
      } else {
        final errorResponse = json.decode(response.body);
        throw ApiException.fromJson(errorResponse);
      }
    } on TimeoutException {
      throw TimeoutException(
          'La solicitud a tardó demasiado, inténtalo nuevamente más tarde.');
    } on ApiException {
      rethrow;
    } catch (e) {
      throw Exception('Error inesperado, inténtalo en un momento');
    }
  }

  Future<String> ingresarSocial(Usuario usuario) async {
    try {
      final response = await http.post(
          Uri.parse('${Ambiente.urlAuth}/social/signup'),
          headers: <String, String>{'Content-Type': 'application/json'},
          body: jsonEncode(usuario));

      if (response.statusCode == 200) {
        final SecureStorage secureStorage = SecureStorage();

        final data = jsonDecode(utf8.decode(response.bodyBytes));
        secureStorage.writeSecureData("refreshToken", data['refreshToken']);
        secureStorage.writeSecureData("user", usuario.correo);
        secureStorage.writeSecureData("loginType", usuario.tipo!);
        return data['accessToken'];
      } else {
        final errorResponse = json.decode(response.body);
        throw ApiException.fromJson(errorResponse);
      }
    } on TimeoutException {
      throw TimeoutException(
          'La solicitud a tardó demasiado, inténtalo nuevamente más tarde.');
    } on ApiException {
      rethrow;
    } catch (e) {
      throw Exception('Error inesperado, inténtalo en un momento');
    }
  }

  Future<Usuario> registrarGoogle() async {
    try {
      await GoogleSignIn().signOut();
      final googleUser = await GoogleSignIn().signIn();

      final googleAuth = await googleUser?.authentication;

      final credential = GoogleAuthProvider.credential(
          accessToken: googleAuth?.accessToken, idToken: googleAuth?.idToken);

      final userCredential =
          await FirebaseAuth.instance.signInWithCredential(credential);

      return Usuario(
          correo: userCredential.user!.email!,
          nombres: userCredential.user!.displayName,
          rrssId: googleUser!.id,
          clave: "dummy",
          tipo: "google");
    } catch (e) {
      throw Exception('Ocurrió un problema al registrarse con google auth');
    }
  }

  Future<Usuario> registrarApple() async {
    try {
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      final oauthCredential = OAuthProvider("apple.com").credential(
          idToken: credential.identityToken,
          accessToken: credential.authorizationCode);

      await FirebaseAuth.instance.signInWithCredential(oauthCredential);

      Usuario? usuarioRrSs;
      if (credential.email == null) {
        usuarioRrSs =
            await UsuarioApi().obtenerUsuarioRrSs(credential.userIdentifier!);
      }

      return Usuario(
          correo: credential.email != null
              ? credential.email!
              : usuarioRrSs!.correo,
          nombres: credential.givenName ?? usuarioRrSs!.nombres,
          rrssId: credential.userIdentifier,
          clave: "dummy",
          tipo: "apple");
    } catch (e) {
      throw Exception('Ocurrió un problema al registrarse con apple auth');
    }
  }

  Future<Usuario> registrarConFacebook() async {
    try {
      final loginResult = await FacebookAuth.instance.login();

      final credential =
          FacebookAuthProvider.credential(loginResult.accessToken!.token);

      final userData = await FacebookAuth.instance.getUserData();

      final userCredential =
          await FirebaseAuth.instance.signInWithCredential(credential);

      return Usuario(
          correo: userCredential.user!.email!,
          nombres: userCredential.user!.displayName,
          rrssId: userData['id'],
          clave: "dummy",
          tipo: "facebook");
    } catch (e) {
      throw Exception('Ocurrió un problema al registrarse con facebook auth');
    }
  }

  Future<String> ingresar(Usuario usuario) async {
    try {
      final response = await http.post(Uri.parse('${Ambiente.urlAuth}/signin'),
          headers: <String, String>{'Content-Type': 'application/json'},
          body: jsonEncode(usuario));

      if (response.statusCode == 200) {
        final SecureStorage secureStorage = SecureStorage();
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        secureStorage.writeSecureData("refreshToken", data['refreshToken']);
        secureStorage.writeSecureData("loginType", "app");
        return data['accessToken'];
      } else {
        final errorResponse = json.decode(response.body);
        throw ApiException.fromJson(errorResponse);
      }
    } on TimeoutException {
      throw TimeoutException(
          'La solicitud a tardó demasiado, inténtalo nuevamente más tarde.');
    } on ApiException {
      rethrow;
    } catch (e) {
      throw Exception('Error inesperado, inténtalo en un momento');
    }
  }

  Future buscarTokenAcceso(String refreshToken) async {
    try {
      final response = await http.post(
          Uri.parse("${Ambiente.urlAuth}/token/refresh"),
          headers: <String, String>{
            'Content-Type': 'application/json; charset=UTF-8',
            'Authorization': 'Bearer $refreshToken'
          });

      if (response.statusCode == 200) {
        final SecureStorage secureStorage = SecureStorage();
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        await secureStorage.writeSecureData("token", data['accessToken']);
        await secureStorage.writeSecureData(
            "refreshToken", data['refreshToken']);
        return;
      } else {
        throw Exception("Error al refrescar el token");
      }
    } catch (e) {
      throw Exception("Error en el servicio al refrescar el token");
    }
  }
}
