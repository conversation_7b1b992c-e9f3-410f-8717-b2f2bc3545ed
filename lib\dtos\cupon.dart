class Cupon {
  final int cuponId;
  final String titulo;
  final String imagen;
  final DateTime fechaVigencia;

  const Cupon(
      {required this.cuponId,
      required this.titulo,
      required this.imagen,
      required this.fechaVigencia});

  static Cupon fromJson(Map<String, dynamic> json) => Cupon(
      cuponId: json['userRewardId'],
      titulo: json['title'],
      imagen: json['image'],
      fechaVigencia: DateTime.parse(json['endDate']));
}
