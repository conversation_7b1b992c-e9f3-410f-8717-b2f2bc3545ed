import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureStorage {
  final _storage = const FlutterSecureStorage();

  Future writeSecureData(String key, String value) async {
    var writeData = await _storage.write(key: key, value: value);
    return writeData;
  }

  Future<String?> readSecureData(String key) async {
    try {
      var readData = await _storage.read(key: key);
      return readData;
    } catch (e) {
      return null;
    }
  }

  Future deleteSecureData(String key) async {
    var deleteData = await _storage.delete(key: key);
    return deleteData;
  }

  Future deleteAll() async {
    await _storage.deleteAll();
  }
}
