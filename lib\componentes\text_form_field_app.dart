import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:myrewards/constantes/colores.dart';

class TextFormFieldApp extends StatefulWidget {
  final TextEditingController? controller;
  final String label;
  final String hint;
  final bool password;
  final bool autoFocus;
  final bool enabled;
  final TextInputType keyboardType;
  final TextInputAction textInputAction;
  final String? Function(String?)? validatior;
  final Function(String)? onFieldSubmitted;
  final TextCapitalization textCapitalization;
  const TextFormFieldApp(
      {super.key,
      this.controller,
      this.validatior,
      this.onFieldSubmitted,
      this.keyboardType = TextInputType.text,
      this.textInputAction = TextInputAction.next,
      required this.label,
      required this.hint,
      this.textCapitalization = TextCapitalization.none,
      this.autoFocus = false,
      this.enabled = true,
      this.password = false});

  @override
  State<TextFormFieldApp> createState() => _TextFormFieldAppState();
}

class _TextFormFieldAppState extends State<TextFormFieldApp> {
  late bool obscureText;

  @override
  void initState() {
    super.initState();
    obscureText = widget.password;
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      validator: widget.validatior,
      enabled: widget.enabled,
      obscureText: obscureText,
      autofocus: widget.autoFocus,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      textCapitalization: widget.textCapitalization,
      style: widget.enabled
          ? const TextStyle(color: ColoresApp.texto)
          : TextStyle(color: Colors.grey.shade400, fontWeight: FontWeight.bold),
      onFieldSubmitted: widget.onFieldSubmitted,
      decoration: InputDecoration(
          filled: true,
          fillColor: ColoresApp.segundario,
          disabledBorder: OutlineInputBorder(
            borderSide: BorderSide(width: 1, color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(20),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(width: 1, color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(20),
          ),
          border: OutlineInputBorder(
            borderSide: BorderSide(width: 1, color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(20),
          ),
          labelText: widget.label,
          hintText: widget.hint,
          suffixIcon: widget.password
              ? IconButton(
                  icon: obscureText
                      ? SvgPicture.asset('assets/images/ojo_abierto.svg',
                          height: 20, width: 20)
                      : SvgPicture.asset('assets/images/ojo_slash.svg',
                          height: 23, width: 23),
                  onPressed: () {
                    setState(() {
                      obscureText = !obscureText;
                    });
                  },
                )
              : null,
          floatingLabelStyle: const TextStyle(color: ColoresApp.texto),
          hintStyle: const TextStyle(color: ColoresApp.texto),
          labelStyle: TextStyle(color: Colors.grey.shade400)),
    );
  }
}
