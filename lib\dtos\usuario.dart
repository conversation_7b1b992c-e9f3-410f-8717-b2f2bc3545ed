class Usuario {
  final String? nombres;
  final String correo;
  final String clave;
  final String? tipo;
  final String? rrssId;
  String? fechaNacimiento;
  
  Usuario(
      {this.nombres,
      required this.correo,
      required this.clave,
      this.fechaNacimiento,
      this.tipo,
      this.rrssId});

  Map<String, dynamic> toJson() {
    return {
      'name': nombres,
      'email': correo,
      'password': clave,
      'type': tipo,
      'socialId': rrssId
    };
  }

  static Usuario fromJson(Map<String, dynamic> json) =>
      Usuario(
        nombres: json['name'], 
        correo: json['email'], 
        fechaNacimiento: json['bday'] ?? "",
        clave: "");
}
