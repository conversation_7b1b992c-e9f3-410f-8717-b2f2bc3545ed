import 'dart:async';
import 'dart:convert';

import 'package:http/http.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:myrewards/constantes/ambiente.dart';
import 'package:myrewards/dtos/comunes/paginado.dart';
import 'package:myrewards/dtos/cupon.dart';
import 'package:myrewards/dtos/cupon_detalle.dart';
import 'package:myrewards/http/auth_interceptor.dart';
import 'package:myrewards/http/retry_policy.dart';
import 'package:myrewards/servicios/exception/api_exception.dart';

class CuponesApi {
  Client client = InterceptedClient.build(
      interceptors: [LoggingInterceptor()],
      retryPolicy: ExpiredTokenRetryPolicy());

  static final CuponesApi _singleton = CuponesApi._internal();

  factory CuponesApi() {
    return _singleton;
  }

  CuponesApi._internal();

  Future<List<Cupon>> misCupones() async {
    try {
      var response = await client.get(Uri.parse("${Ambiente.urlCupon}/all"));
      if (response.statusCode == 200) {
        final Paginado paginado =
            Paginado.fromJson(jsonDecode(utf8.decode(response.bodyBytes)));
        List<Cupon> datos =
            paginado.content.map((e) => Cupon.fromJson(e)).toList();
        return datos;
      }
    } catch (e) {
      //controlar exception
    }

    return [];
  }

  Future<CuponDetalle?> buscarCupon(int id) async {
    try {
      var response = await client.get(Uri.parse("${Ambiente.urlCupon}/$id"));
      if (response.statusCode == 200) {
        return CuponDetalle.fromJson(
            jsonDecode(utf8.decode(response.bodyBytes)));
      }
    } catch (e) {
      //controlar exception
    }

    return null;
  }

  Future borrarCupon(int id) async {
    try {
      var response =
          await client.delete(Uri.parse("${Ambiente.urlCupon}/clients/$id"));
      if (response.statusCode == 202) {
        return;
      } else {
        final errorResponse = json.decode(response.body);
        throw ApiException.fromJson(errorResponse);
      }
    } on TimeoutException {
      throw TimeoutException(
          'La solicitud a tardó demasiado, inténtalo nuevamente más tarde.');
    } on ApiException {
      rethrow;
    } catch (e) {
      throw Exception('Error inesperado, inténtalo en un momento');
    }
  }

  Future aceptarCupon(String codigo) async {
    try {
      var response =
          await client.post(Uri.parse("${Ambiente.urlCupon}/accept/$codigo"));
      if (response.statusCode == 202) {
        return;
      } else {
        final errorResponse = json.decode(response.body);
        throw ApiException.fromJson(errorResponse);
      }
    } on TimeoutException {
      throw TimeoutException(
          'La solicitud a tardó demasiado, inténtalo nuevamente más tarde.');
    } on ApiException {
      rethrow;
    } catch (e) {
      throw Exception('Error inesperado, inténtalo en un momento');
    }
  }

  Future agregarPorCodigo(String codigo) async {
    try {
      var response =
          await client.put(Uri.parse("${Ambiente.urlCupon}/clients/$codigo"));
      if (response.statusCode == 202) {
        return;
      } else {
        final errorResponse = json.decode(response.body);
        throw ApiException.fromJson(errorResponse);
      }
    } on TimeoutException {
      throw TimeoutException(
          'La solicitud a tardó demasiado, inténtalo nuevamente más tarde.');
    } on ApiException {
      rethrow;
    } catch (e) {
      throw Exception('Error inesperado, inténtalo en un momento');
    }
  }
}
