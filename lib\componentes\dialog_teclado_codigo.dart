import 'package:flutter/material.dart';
import 'package:myrewards/componentes/text_form_field_app.dart';
import 'package:myrewards/constantes/colores.dart';

class TecladoCodigoDialog extends StatefulWidget {
  const TecladoCodigoDialog({super.key});

  @override
  State<TecladoCodigoDialog> createState() => _TecladoCodigoDialogState();
}

class _TecladoCodigoDialogState extends State<TecladoCodigoDialog> {
  final TextEditingController tecCodigo = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        elevation: 0,
        backgroundColor: ColoresApp.fondo,
        child: Stack(children: [
          Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: Column(mainAxisSize: MainAxisSize.min, children: [
                const SizedBox(height: 35),
                TextForm<PERSON>ieldApp(
                    controller: tec<PERSON>od<PERSON>,
                    label: "Escribe el código",
                    hint: "Ingresa acá el código",
                    textCapitalization: TextCapitalization.characters,
                    autoFocus: true),
                const SizedBox(height: 8),
                SizedBox(
                  height: 55,
                  width: double.infinity,
                  child: ElevatedButton(
                      onPressed: () {
                        debugPrint("Codigo de salida ${tecCodigo.text}");
                        Navigator.of(context).pop(tecCodigo.text);
                      },
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                      child: const Text("Agregar",
                          style: TextStyle(
                              fontSize: 16, fontWeight: FontWeight.bold))),
                ),
                const SizedBox(height: 8)
              ])),
          Positioned(
            top: 0,
            right: 0,
            child: IconButton(
                iconSize: 30,
                icon: const Icon(
                  Icons.cancel,
                  color: Colors.grey,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                }),
          )
        ]));
  }
}
