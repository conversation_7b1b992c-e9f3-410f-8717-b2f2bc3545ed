import 'package:flutter/material.dart';

class CargandoUtil {
  static final CargandoUtil _singleton = CargandoUtil._internal();

  factory CargandoUtil() {
    return _singleton;
  }

  CargandoUtil._internal();

  cargando(String mensaje, BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(width: 16),
                Text(mensaje),
              ],
            ),
          ),
        );
      },
    );
  }
}
