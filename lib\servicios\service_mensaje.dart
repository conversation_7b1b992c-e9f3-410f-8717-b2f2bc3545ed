import 'dart:async';
import 'dart:convert';

import 'package:http/http.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:myrewards/constantes/ambiente.dart';
import 'package:myrewards/dtos/mensaje.dart';
import 'package:myrewards/http/auth_interceptor.dart';
import 'package:myrewards/http/retry_policy.dart';
import 'package:myrewards/servicios/database/service_database.dart';
import 'package:myrewards/storage/secure_storage.dart';

class MensajeApi {
  Client client = InterceptedClient.build(
      interceptors: [LoggingInterceptor()],
      retryPolicy: ExpiredTokenRetryPolicy());

  static final MensajeApi _singleton = MensajeApi._internal();

  factory MensajeApi() {
    return _singleton;
  }

  MensajeApi._internal();

  Future<List<Mensaje>> misMensajes() async {
    List<Mensaje> datos = [];
    final dbService = DatabaseService.instance;
    final SecureStorage secureStorage = SecureStorage();
    final usuario = await secureStorage.readSecureData("user") ?? "usuario";
    final loginType = await secureStorage.readSecureData("loginType") ?? "_";

    List<Mensaje> mensajesActuales = [];

    try {
      mensajesActuales = await dbService.getMensajes(usuario + loginType);
    } catch (e) {
      //controlar exception
    }

    try {
      var response = await client.get(Uri.parse("${Ambiente.urlMensaje}/user"));
      if (response.statusCode == 200) {
        final List data = jsonDecode(utf8.decode(response.bodyBytes));
        List<Mensaje> datosNuevos =
            data.map((e) => Mensaje.fromJson(e)).toList();

        await marcarComoRecibidos(datosNuevos.map((e) => e.id).toList());

        for (var mensaje in datosNuevos) {
          mensaje.usuarioLlave = usuario + loginType;
          await dbService.insertMensaje(mensaje);
        }

        datos.addAll(datosNuevos);
      }
    } catch (e) {
      //controlar exception
    }

    datos.addAll(mensajesActuales);

    return datos;
  }

  Future<int> misMensajesContar() async {
    try {
      var response =
          await client.get(Uri.parse("${Ambiente.urlMensaje}/count/user"));
      if (response.statusCode == 200) {
        final Map<String, dynamic> data =
            jsonDecode(utf8.decode(response.bodyBytes));
        return data["newMessages"];
      }
    } catch (e) {
      //controlar exception
    }

    return 0;
  }

  Future marcarComoRecibidos(List<int> mensajeIds) async {
    try {
      await client.put(
        Uri.parse("${Ambiente.urlMensaje}/mark/received"),
        body: '[${mensajeIds.join(",")}]',
      );
    } on Exception {
      //controlar exception
    }
  }

  Future marcarComoLeido(int mensajeId) async {
    try {
      await client
          .put(Uri.parse("${Ambiente.urlMensaje}/mark/viewed/$mensajeId"));
      final dbService = DatabaseService.instance;
      await dbService.actualizarEstado(mensajeId, "VIEWED");
    } on Exception {
      //controlar exception
    }
  }

  Future<void> eliminarMensaje(int mensajeId) async {
    try {
      final dbService = DatabaseService.instance;
      await dbService.deleteMensajeById(mensajeId);
    } on Exception {
      //controlar exception
    }
  }
}
