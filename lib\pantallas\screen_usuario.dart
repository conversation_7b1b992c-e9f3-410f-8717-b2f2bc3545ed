import 'dart:developer';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter/material.dart';
import 'package:myrewards/componentes/dialog_confirmacion.dart';
import 'package:myrewards/componentes/text_form_field_app.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:myrewards/estados/state_usuario.dart';
import 'package:myrewards/servicios/service_usuario.dart';
import 'package:myrewards/storage/secure_storage.dart';
import 'package:go_router/go_router.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

class UsuarioScreen extends StatefulWidget {
  const UsuarioScreen({super.key});

  @override
  State<UsuarioScreen> createState() => _UsuarioScreenState();
}

class _UsuarioScreenState extends State<UsuarioScreen> {
  final TextEditingController _tecNombres = TextEditingController();
  final TextEditingController _tecFechaNacimiento = TextEditingController();
  final TextEditingController _tecCorreo = TextEditingController();
  late String appVersion = "";

  void buscarUsuario() {
    UsuarioApi().obtenerUsuario().then((usuario) {
      if (usuario != null) {
        //

        setState(() {
          _tecNombres.text = usuario.nombres!;
          _tecCorreo.text = usuario.correo;
          _tecFechaNacimiento.text = usuario.fechaNacimiento ?? "";
        });

        context.read<UsuarioState>().setUsuario(usuario);
      }
    });
  }

  Future salir() async {
    final SecureStorage secureStorage = SecureStorage();
    var loginType = await secureStorage.readSecureData("loginType");
    await FirebaseAuth.instance.signOut();
    if (loginType == 'google') {
      await GoogleSignIn().signOut();
    } else if (loginType == 'facebook') {
      await FacebookAuth.instance.logOut();
    }

    await secureStorage.deleteAll();
  }

  @override
  void initState() {
    super.initState();
    obtenerVersion();
    buscarUsuario();
  }

  @override
  void dispose() {
    _tecNombres.dispose();
    _tecCorreo.dispose();
    super.dispose();
  }

  void obtenerVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    appVersion = packageInfo.version;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          foregroundColor: ColoresApp.texto,
          backgroundColor: ColoresApp.acento,
          elevation: 0,
          title: Row(
            children: [
              const Expanded(
                child: Text("Mi cuenta",
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        fontWeight: FontWeight.bold, color: Colors.white)),
              ),
              Padding(
                padding: const EdgeInsets.only(right: 16.0),
                child: Text("v$appVersion",
                    textAlign: TextAlign.right,
                    style: const TextStyle(color: Colors.white, fontSize: 12)),
              )
            ],
          )),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.only(top: 21, left: 16, right: 16),
          child: Column(children: [
            TextFormFieldEditable(
              label: "Nombres",
              type: 'text',
              controller: _tecNombres,
              onSave: (value) {
                if (_tecNombres.text.isNotEmpty) {
                  UsuarioApi().actualizarValor("name", _tecNombres.text);
                }
              },
            ),
            const SizedBox(height: 8),
            TextFormFieldEditable(
              label: "Fecha de nacimiento",
              type: 'date',
              controller: _tecFechaNacimiento,
              onSave: (value) {
                if (value != null && value.isNotEmpty) {
                  UsuarioApi().actualizarValor("bday", value);
                  context.read<UsuarioState>().setFechaNacimiento(value);
                }
              },
            ),
            const SizedBox(height: 16),
            TextFormFieldApp(
                controller: _tecCorreo,
                enabled: false,
                label: "Correo electrónico",
                hint: "Escribe tu correo acá"),
            const SizedBox(height: 38),
            SizedBox(
                width: double.infinity,
                height: 55,
                child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white, // Color de fondo
                      foregroundColor: ColoresApp.texto, // Color del texto
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(20), // Bordes redondeados
                        side: const BorderSide(
                            color: ColoresApp
                                .acento), // Borde con color personalizado
                      ),
                    ),
                    onPressed: () {
                      salir().then((_) {
                        context.go('/ingreso');
                      });
                    },
                    child: const Text(
                      "Cerrar sesión",
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ))),
            const SizedBox(height: 16),
            SizedBox(
                width: double.infinity,
                height: 55,
                child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white, // Color de fondo
                      foregroundColor: ColoresApp.texto, // Color del texto
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(20), // Bordes redondeados
                        side: const BorderSide(
                            color: ColoresApp
                                .acento), // Borde con color personalizado
                      ),
                    ),
                    onPressed: () {
                      showDialog<String>(
                          context: context,
                          builder: (context) {
                            return const ConfirmacionDialog(
                                mensaje:
                                    "¿Está seguro de eliminar su cuenta?, perderás toda tu información");
                          }).then((value) {
                        if (value == 'T') {
                          final Uri url =
                              Uri.parse('https://myreward.cl/eliminarCuenta');
                          launchUrl(url);
                        }
                      });
                    },
                    child: const Text(
                      "Eliminar cuenta",
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ))),
          ]),
        ),
      ),
    );
  }
}

class TextFormFieldEditable extends StatefulWidget {
  final TextEditingController controller;
  final String label;
  final Function? onTap;
  final Function(String? value) onSave;
  final String type;
  final bool showAction;

  const TextFormFieldEditable(
      {super.key,
      required this.controller,
      required this.label,
      required this.type,
      required this.onSave,
      this.onTap,
      this.showAction = true});

  @override
  State<TextFormFieldEditable> createState() => _TextFormFieldEditableState();
}

class _TextFormFieldEditableState extends State<TextFormFieldEditable> {
  bool isEditing = false;
  final FocusNode _focusNode = FocusNode();
  bool displayBtnAction = true;

  @override
  void initState() {
    super.initState();
    log('iniciando ${widget.showAction} ${widget.type} - ${widget.controller.text}');
    displayBtnAction = widget.showAction;
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  void seleccionarFecha() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime(2000),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      locale: const Locale('es', 'ES'),
    );
    if (picked != null) {
      widget.onSave(
          "${picked.day.toString().padLeft(2, '0')}/${picked.month.toString().padLeft(2, '0')}/${picked.year}");
      setState(() {
        widget.controller.text =
            "${picked.day.toString().padLeft(2, '0')}/${picked.month.toString().padLeft(2, '0')}/${picked.year}";
        isEditing = false;
        displayBtnAction = false;
      });
    } else {
      setState(() {
        isEditing = false;
      });
    }
  }

  bool checkShowAction() {
    if (widget.type == 'date') {
      if (context.watch<UsuarioState>().usuario != null) {
        log('fecha ${context.watch<UsuarioState>().usuario!.fechaNacimiento}');
        return context.watch<UsuarioState>().usuario!.fechaNacimiento == null ||
            context.watch<UsuarioState>().usuario!.fechaNacimiento!.isEmpty;
      }
      return true;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 65,
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 13),
            child: Container(
              height: 52,
              decoration: BoxDecoration(
                color: ColoresApp.segundario,
                border: Border.all(color: Colors.grey.shade300, width: 1),
                borderRadius: BorderRadius.circular(
                    20), // El radio por defecto de ElevatedButton
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(left: 12),
                      child: TextFormField(
                        controller: widget.controller,
                        enabled: isEditing,
                        focusNode: _focusNode,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                        ),
                        style: isEditing
                            ? const TextStyle(color: ColoresApp.texto)
                            : TextStyle(
                                color: Colors.grey.shade400,
                                fontWeight: FontWeight.bold),
                        onFieldSubmitted: (value) {
                          if (value.isNotEmpty) {
                            setState(() {
                              isEditing = false;
                            });
                            widget.onSave(widget.controller.text);
                          }
                        },
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Visibility(
                    visible: checkShowAction(),
                    child: IconButton(
                      icon: Icon(isEditing
                          ? Icons.check
                          : widget.type == 'date'
                              ? Icons.calendar_month
                              : Icons.edit),
                      onPressed: () {
                        setState(() {
                          if (isEditing) {
                            if (widget.controller.text.isNotEmpty) {
                              widget.onSave(widget.controller.text);
                              isEditing = false;
                            }
                          } else {
                            isEditing = true;
                            if (widget.type == 'date') {
                              seleccionarFecha();
                            } else {
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                _focusNode.requestFocus();
                              });
                            }
                          }
                        });
                      },
                    ),
                  )
                ],
              ),
            ),
          ),
          Positioned(
            top: 3,
            left: 10,
            child: Container(
                color: ColoresApp.fondo,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child:
                      Text(widget.label, style: const TextStyle(fontSize: 12)),
                )),
          )
        ],
      ),
    );
  }
}
