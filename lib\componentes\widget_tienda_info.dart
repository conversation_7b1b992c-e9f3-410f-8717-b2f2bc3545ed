import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:myrewards/dtos/campacna.dart';
import 'package:myrewards/util/fecha_util.dart';

class TiendaInfoWidget extends StatefulWidget {
  final Campacna campacna;
  const TiendaInfoWidget({super.key, required this.campacna});

  @override
  State<TiendaInfoWidget> createState() => _TiendaInfoWidgetState();
}

class _TiendaInfoWidgetState extends State<TiendaInfoWidget> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 8),
            const Text("Recompensa", style: TextStyle(color: ColoresApp.texto)),
            const SizedBox(height: 4),
            Text(widget.campacna.recompensaTitulo,
                style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: ColoresApp.texto)),
            const SizedBox(height: 16),
            const Text("Tarjeta válida hasta",
                style: TextStyle(color: ColoresApp.texto)),
            const SizedBox(height: 4),
            Text(
                "${DateFormat('dd-MM-yyyy').format(widget.campacna.recompensaValidaHasta)} (quedan ${FechaUtil().calcularDias(widget.campacna.recompensaValidaHasta)} días)",
                style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: ColoresApp.texto)),
            const SizedBox(height: 16),
            const Text("Cantidad de estampillas",
                style: TextStyle(color: ColoresApp.texto)),
            const SizedBox(height: 4),
            Text(widget.campacna.estampillasTarjeta.toString(),
                style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: ColoresApp.texto)),
            const SizedBox(height: 16),
            const Text("Condición", style: TextStyle(color: ColoresApp.texto)),
            const SizedBox(height: 4),
            Text(widget.campacna.recompensaCondicionCorta,
                style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: ColoresApp.texto)),
            const SizedBox(height: 16),
            const Text("Letra chica",
                style: TextStyle(color: ColoresApp.texto)),
            const SizedBox(height: 4),
            Text(widget.campacna.recompensaCondicionLarga,
                style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: ColoresApp.texto)),
            const SizedBox(height: 8),
          ]),
    );
  }
}
