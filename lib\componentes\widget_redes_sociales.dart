import 'dart:developer';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:myrewards/dtos/tienda_red_social.dart';
import 'package:myrewards/servicios/service_tienda.dart';

class RedesSocialesWidget extends StatefulWidget {
  final String codigoTienda;
  const RedesSocialesWidget({super.key, required this.codigoTienda});

  @override
  State<RedesSocialesWidget> createState() => _RedesSocialesWidgetState();
}

class _RedesSocialesWidgetState extends State<RedesSocialesWidget> {
  List<TiendaRedSocial> _redesSociales = [];
  final double _containerSize = 40;

  Future<void> obtenerRedesSociales() async {
    List<TiendaRedSocial> redes =
        await TiendaApi().redesSociales(widget.codigoTienda);
    setState(() {
      _redesSociales = redes;
      log("redes sociales: ${_redesSociales.length}");
    });
  }

  @override
  void initState() {
    super.initState();
    obtenerRedesSociales();
  }

  Future<void> _launchURL(String url) async {
    final uri = Uri.parse(Uri.encodeFull(url.trim()));
    try {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } catch (e) {
      log('Error al abrir la URL: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return _redesSociales.isEmpty
        ? const SizedBox.shrink()
        : SizedBox(
            height: 60,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: _redesSociales.map((redSocial) {
                final String tipo = redSocial.tipo.toLowerCase();
                return GestureDetector(
                  onTap: () => _launchURL(redSocial.url),
                  child: SizedBox(
                    width: _containerSize + 10,
                    height: _containerSize,
                    child: Center(
                      child: SvgPicture.asset(
                        'assets/images/rrss_$tipo.svg',
                        height: _containerSize - 10,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          );
  }
}
