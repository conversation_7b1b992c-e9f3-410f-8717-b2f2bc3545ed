import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:myrewards/componentes/dialog_tienda_info.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:myrewards/estados/state_tiendas_destacadas.dart';
import 'package:myrewards/pantallas/inicio/widgets/widget_card_destacado.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:provider/provider.dart';


class DescatadosWidget extends StatefulWidget {
  const DescatadosWidget({super.key});

  @override
  State<DescatadosWidget> createState() => _DescatadosWidgetState();
}

class _DescatadosWidgetState extends State<DescatadosWidget> {

  final controller = PageController(viewportFraction: 0.9);
  
  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Padding(
        padding: const EdgeInsets.only(top: 8, left: 16, right: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text("Tarjetas destacadas",
                style: TextStyle(
                    fontWeight: FontWeight.bold, color: ColoresApp.texto)),
            Visibility(
              visible: context.watch<TiendasDestacadasState>().hasMoreData,
              child: GestureDetector(
                onTap: () {
                  context.push('/verMas');
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                  color: Colors.transparent, // Color transparente para aumentar el área táctil
                  child: const Text("Ver más",
                      style: TextStyle(
                          fontSize: 12, color: ColoresApp.acento)),
                ),
              ),
            ),
          ],
        )
      ),
      const SizedBox(height: 8),
      Container(
        color: Colors.transparent,
        height: 205,
        child: PageView.builder(
            controller: controller,
            itemCount: context.watch<TiendasDestacadasState>().tiendasDestacadas.length + 
                      (context.watch<TiendasDestacadasState>().hasMoreData ? 1 : 0),
            itemBuilder: (context, index) {
              // Si es el último ítem y hasMoreData es true, mostrar el card "Ver más"
              if (index == context.watch<TiendasDestacadasState>().tiendasDestacadas.length && 
                  context.watch<TiendasDestacadasState>().hasMoreData) {
                return GestureDetector(
                  onTap: () {
                    context.push('/verMas');
                  },
                  child: Container(
                    margin: const EdgeInsets.only(right: 8.0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: ColoresApp.acento, width: 1),
                      color: Colors.white,
                    ),
                    child: const Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.add_circle_outline_rounded, 
                             size: 60, 
                             color: ColoresApp.acento),
                        SizedBox(height: 16),
                        Text("Otras recompensas cerca de ti",
                            style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: ColoresApp.acento)),
                      ],
                    ),
                  ),
                );
              }
              
              // Para los ítems normales
              return GestureDetector(
                onTap: () {
                  showDialog(
                      context: context,
                      builder: (context) {
                        return TiendaInfoDialog(
                          tiendaCodigo: context
                              .read<TiendasDestacadasState>()
                              .tiendasDestacadas[index]
                              .codigo,
                        );
                      });
                },
                child: CardDestacadoWidget(tiendaDestacada: 
                    context.read<TiendasDestacadasState>().tiendasDestacadas[index]),
              );
            }),
      ),
      SizedBox(
          height: 30,
          width: double.infinity,
          child: Align(
            alignment: Alignment.center,
            child: SmoothPageIndicator(
                controller: controller,
                count: context.watch<TiendasDestacadasState>().tiendasDestacadas.length + 
                      (context.watch<TiendasDestacadasState>().hasMoreData ? 1 : 0),
                effect: const WormEffect(
                    spacing: 8.0,
                    radius: 4.0,
                    dotWidth: 6.0,
                    dotHeight: 6.0,
                    dotColor: ColoresApp.amarillo,
                    activeDotColor: ColoresApp.acento)),
          ))
    ]));
  }
}
