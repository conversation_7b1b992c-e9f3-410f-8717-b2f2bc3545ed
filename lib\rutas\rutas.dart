import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:myrewards/dtos/mensaje.dart';
import 'package:myrewards/pantallas/inicio/ver_mas/screen_ver_mas.dart';
import 'package:myrewards/pantallas/screen_detalle_cupon.dart';
import 'package:myrewards/pantallas/screen_detalle_mensaje.dart';
import 'package:myrewards/pantallas/screen_detalle_tarjeta.dart';
import 'package:myrewards/pantallas/screen_ingreso.dart';
import 'package:myrewards/pantallas/screen_intro.dart';
import 'package:myrewards/pantallas/screen_lector_qr.dart';
import 'package:myrewards/pantallas/screen_marco.dart';
import 'package:myrewards/pantallas/screen_mensajes.dart';
import 'package:myrewards/pantallas/screen_registro.dart';
import 'package:myrewards/pantallas/screen_tienda.dart';
import 'package:myrewards/storage/secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppRouter {
  late GoRouter router;

  static final AppRouter _instance = AppRouter._internal();

  factory AppRouter() {
    return _instance;
  }

  AppRouter._internal();

  static AppRouter get instance => _instance;

  void setRouter(GoRouter router) {
    this.router = router;
  }

  static GoRouter initRouter() {
    return GoRouter(routes: <RouteBase>[
      GoRoute(
          path: '/intro',
          builder: (BuildContext context, GoRouterState state) {
            return const IntroScreen();
          }),
      GoRoute(
          path: '/inicio',
          builder: (BuildContext context, GoRouterState state) {
            return const MarcoScreen();
          }),
      GoRoute(
          path: '/inicio/recompensaQr/:codigo',
          builder: (BuildContext context, GoRouterState state) {
            final timestamp = state.uri.queryParameters['t'];
            return MarcoScreen(
                recompensaQrCodigo: state.pathParameters['codigo'],
                timestamp: timestamp);
          }),
      GoRoute(
          path: '/inicio/:codigo',
          builder: (BuildContext context, GoRouterState state) {
            final timestamp = state.uri.queryParameters['t'];
            return MarcoScreen(
                shoppingCode: state.pathParameters['codigo'],
                timestamp: timestamp);
          }),
      GoRoute(
          path: '/verMas',
          builder: (BuildContext context, GoRouterState state) {
            return const VerMasScreen();
          }),
      GoRoute(
          path: '/tarjeta/:codigo/:codigoTienda',
          builder: (BuildContext context, GoRouterState state) {
            return DetalleTarjetaScreen(
                codigoTarjeta: state.pathParameters["codigo"]!,
                codigoTienda: state.pathParameters["codigoTienda"]!);
          }),
      GoRoute(
          path: '/cupon/:id',
          builder: (BuildContext context, GoRouterState state) {
            return DetalleCuponScreen(
                id: int.parse(state.pathParameters["id"]!));
          }),
      GoRoute(
          path: '/registro',
          builder: (BuildContext context, GoRouterState state) {
            return const RegistroScreen();
          }),
      GoRoute(
          path: '/lectura',
          builder: (BuildContext context, GoRouterState state) {
            return const LectorQrScreen();
          }),
      GoRoute(
          path: '/ingreso',
          builder: (BuildContext context, GoRouterState state) {
            return const IngresoScreen();
          }),
      GoRoute(
          path: '/mensajes',
          builder: (BuildContext context, GoRouterState state) {
            return const MensajesScreen();
          }),
      GoRoute(
          path: '/mensaje/detalle',
          builder: (BuildContext context, GoRouterState state) {
            return DetalleMensajeScreen(mensaje: state.extra as Mensaje);
          }),
      GoRoute(
          path: '/ingreso/:motivo',
          builder: (BuildContext context, GoRouterState state) {
            return IngresoScreen(motivo: state.pathParameters['motivo']);
          }),
      GoRoute(
          path: '/tienda/:codigo',
          builder: (BuildContext context, GoRouterState state) {
            return TiendaScreen(shoppingCode: state.pathParameters['codigo']!);
          }),
      GoRoute(
          path: '/shopping/:codigo',
          redirect: (context, state) async {
            final prefs = await SharedPreferences.getInstance();
            final SecureStorage secureStorage = SecureStorage();

            if (prefs.getBool('primeraVez') ?? true) {
              await secureStorage.deleteAll();
              return "/intro";
            } else {
              String? token = await secureStorage.readSecureData("token");
              if (token == null) {
                return "/ingreso";
              }
            }

            final timestamp = DateTime.now().millisecondsSinceEpoch;
            return "/inicio/${state.pathParameters['codigo']}?t=$timestamp";
          }),
      GoRoute(
          path: '/qrCode/:codigo',
          redirect: (context, state) async {
            final prefs = await SharedPreferences.getInstance();
            final SecureStorage secureStorage = SecureStorage();

            if (prefs.getBool('primeraVez') ?? true) {
              await secureStorage.deleteAll();
              return "/intro";
            } else {
              String? token = await secureStorage.readSecureData("token");
              if (token == null) {
                return "/ingreso";
              }
            }

            final timestamp = DateTime.now().millisecondsSinceEpoch;
            return "/inicio/recompensaQr/${state.pathParameters['codigo']}?t=$timestamp";
          }),
      GoRoute(
          path: '/previo',
          redirect: (context, state) async {
            try {
              final prefs = await SharedPreferences.getInstance();
              final SecureStorage secureStorage = SecureStorage();

              if (prefs.getBool('primeraVez') ?? true) {
                await secureStorage.deleteAll();
                return "/intro";
              } else {
                String? token = await secureStorage.readSecureData("token");
                if (token == null) {
                  return "/ingreso";
                }
              }
            } catch (e) {
              return "/ingreso";
            }

            return "/inicio";
          }),
    ], initialLocation: '/previo');
  }
}
