class TiendaDetalle {
  final int id;
  final String nombre;
  final String telefono;
  final String direccion;
  final String descripcion;
  final String imagen;
  final double latitud;
  final double longitud;

  const TiendaDetalle(
      {required this.id,
      required this.nombre,
      required this.telefono,
      required this.direccion,
      required this.descripcion,
      required this.imagen,
      required this.latitud,
      required this.longitud});

  static TiendaDetalle fromJson(Map<String, dynamic> json) => TiendaDetalle(
      id: json['id'],
      nombre: json['name'],
      telefono: json['phone'],
      direccion: json['address'],
      descripcion: json['description'],
      imagen: json['image'],
      latitud: json['latitude'],
      longitud: json['longitude']);
}
