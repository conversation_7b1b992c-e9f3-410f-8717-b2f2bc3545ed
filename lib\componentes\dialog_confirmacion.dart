import 'package:flutter/material.dart';
import 'package:myrewards/constantes/colores.dart';

class ConfirmacionDialog extends StatefulWidget {
  final String mensaje;
  final String botonAceptar;
  const ConfirmacionDialog(
      {super.key, required this.mensaje, this.botonAceptar = "Aceptar"});

  @override
  State<ConfirmacionDialog> createState() => _ConfirmacionDialogState();
}

class _ConfirmacionDialogState extends State<ConfirmacionDialog> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        elevation: 0,
        backgroundColor: ColoresApp.fondo,
        child: Stack(children: [
          Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: Column(mainAxisSize: MainAxisSize.min, children: [
                const SizedBox(height: 25),
                Text(
                  widget.mensaje,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 24, color: ColoresApp.texto),
                ),
                const SizedBox(height: 24),
                SizedBox(
                  height: 55,
                  width: double.infinity,
                  child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                            height: 55,
                            child: ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop("T");
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      Colors.white, // Color de fondo
                                  foregroundColor:
                                      ColoresApp.texto, // Color del texto
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(
                                        20), // Bordes redondeados
                                    side: const BorderSide(
                                        color: ColoresApp
                                            .acento), // Borde con color personalizado
                                  ),
                                ),
                                child: Text(widget.botonAceptar,
                                    style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold)))),
                        const SizedBox(width: 8),
                        SizedBox(
                            height: 55,
                            child: ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop("F");
                                },
                                style: ElevatedButton.styleFrom(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                ),
                                child: const Text("Cancelar",
                                    style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold))))
                      ]),
                ),
                const SizedBox(height: 8)
              ])),
          Positioned(
            top: 0,
            right: 0,
            child: IconButton(
                iconSize: 30,
                icon: const Icon(
                  Icons.cancel,
                  color: Colors.grey,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                }),
          )
        ]));
  }
}
