import 'package:flutter/material.dart';
import 'package:myrewards/dtos/cupon.dart';
import 'package:myrewards/servicios/service_cupones.dart';

class CuponesState with ChangeNotifier {
  List<Cupon> _cupones = [];
  bool _nuevoCupon = false;
  bool _cargando = true;

  List<Cupon> get cupones => _cupones;
  bool get cargando => _cargando;
  bool get nuevoCupon => _nuevoCupon;

  set cupones(value) {
    _cupones = value;
    notifyListeners();
  }

  set nuevoCupon(value) {
    _nuevoCupon = value;
    notifyListeners();
  }

  agregarCupon(Cupon cupon) {
    _cupones.add(cupon);
    notifyListeners();
  }

  misCupones() async {
    _cupones = await CuponesApi().misCupones();
    _cargando = false;
    notifyListeners();
  }

  quitarCupon(int id) {
    _cupones = _cupones.where((element) => element.cuponId != id).toList();
    notifyListeners();
  }
}
