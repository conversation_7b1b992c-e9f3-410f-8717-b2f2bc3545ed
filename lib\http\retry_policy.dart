import 'package:http_interceptor/http_interceptor.dart';
import 'package:myrewards/rutas/rutas.dart';
import 'package:myrewards/servicios/service_autorizacion.dart';
import 'package:myrewards/storage/secure_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';

class ExpiredTokenRetryPolicy extends RetryPolicy {
  @override
  int get maxRetryAttempts => 3;

  @override
  bool shouldAttemptRetryOnException(Exception reason) {
    return true;
  }

  @override
  Future<bool> shouldAttemptRetryOnResponse(ResponseData response) async {
    if (response.statusCode == 401 || response.statusCode == 403) {
      final SecureStorage secureStorage = SecureStorage();
      String refreshToken =
          await secureStorage.readSecureData("refreshToken") ?? "";

      try {
        await AuthApi().buscarTokenAcceso(refreshToken);
      } catch (e) {
        await salir();
        AppRouter.instance.router.go("/ingreso");
        return false;
      }

      return true;
    }

    return false;
  }

  Future salir() async {
    final SecureStorage secureStorage = SecureStorage();
    try {
      var loginType = await secureStorage.readSecureData("loginType");
      await FirebaseAuth.instance.signOut();
      if (loginType == 'google') {
        await GoogleSignIn().signOut();
      } else if (loginType == 'facebook') {
        await FacebookAuth.instance.logOut();
      }
    } catch (e) {
      Future.error("Error al salir social");
    }

    await secureStorage.deleteAll();
  }
}
