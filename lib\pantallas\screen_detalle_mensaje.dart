import 'package:flutter/material.dart';
import 'package:myrewards/componentes/dialog_tienda_info.dart';
import 'package:myrewards/componentes/widger_tienda_mapa.dart';
import 'package:myrewards/constantes/ambiente.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:myrewards/dtos/mensaje.dart';
import 'package:myrewards/estados/state_cupones.dart';
import 'package:myrewards/estados/state_mensajes.dart';
import 'package:myrewards/servicios/exception/api_exception.dart';
import 'package:myrewards/servicios/service_cupones.dart';
import 'package:myrewards/util/cargando_util.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

class DetalleMensajeScreen extends StatefulWidget {
  final Mensaje mensaje;

  const DetalleMensajeScreen({super.key, required this.mensaje});

  @override
  State<DetalleMensajeScreen> createState() => _DetalleMensajeScreenState();
}

class _DetalleMensajeScreenState extends State<DetalleMensajeScreen> {
  @override
  void initState() {
    super.initState();
    context.read<MensajesState>().marcarComoLeido(widget.mensaje.id);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.mensaje.tienda ?? 'Detalle Mensaje'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (widget.mensaje.imagen != null)
                    Center(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12.0),
                        child: Image.network(
                          '${Ambiente.urlImagenes}/messages/${widget.mensaje.imagen!}',
                          fit: BoxFit.cover,
                          width: 200.0,
                          height: 200.0,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey[300],
                              height: 200.0,
                              child: const Center(
                                child: Icon(Icons.broken_image,
                                    size: 50, color: Colors.grey),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  const SizedBox(height: 16.0),
                  Text(
                    widget.mensaje.titulo,
                    style: const TextStyle(
                      fontSize: 16.0,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8.0),
                  Text(
                    widget.mensaje.mensaje,
                    style: const TextStyle(
                      fontSize: 12.0,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 21),
                  if (widget.mensaje.tipo == 'REWARD')
                    TiendaMapaWidget(tiendaId: widget.mensaje.tiendaId!),
                ],
              ),
            ),
          ),
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: _buildActionButton(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton() {
    if (widget.mensaje.tipo == 'ADVERTISING' || widget.mensaje.tipo == 'APP') {
      return const SizedBox.shrink();
    }

    return ElevatedButton(
      onPressed: () {
        if (widget.mensaje.tipo == 'REWARD') {
          agregarRecompensa(widget.mensaje.data);
        } else if (widget.mensaje.tipo == 'CARD') {
          showDialog<String>(
            context: context,
            builder: (context) {
              return TiendaInfoDialog(
                tiendaCodigo: widget.mensaje.data!,
              );
            },
          ).then((result) {
            if (result == 'A') {
              Navigator.of(context).pop();
            }
          });
        } else if (widget.mensaje.tipo == 'STORE') {
          context.go('/tienda/${widget.mensaje.data}');
        }
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: ColoresApp.acento,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        minimumSize: const Size(double.infinity, 50),
      ),
      child: Text(
        widget.mensaje.tipo == 'STORE'
            ? 'Ver tienda'
            : widget.mensaje.tipo == 'CARD'
                ? 'Agregar Tarjeta'
                : widget.mensaje.tipo == 'REWARD'
                    ? 'Agregar recompensa'
                    : 'Canjear',
        style: const TextStyle(fontSize: 16.0, color: Colors.white),
      ),
    );
  }

  agregarRecompensa(String? data) {
    CargandoUtil().cargando("Agregando ...", context);
    CuponesApi().aceptarCupon(data!).then((onValue) {
      Navigator.of(context).pop();
      context.read<CuponesState>().misCupones();
      context.read<CuponesState>().nuevoCupon = true;
      Navigator.of(context).pop();
    }, onError: (e) {
      if (mounted) {
        Navigator.of(context).pop();
        String errorMessage = e is ApiException ? e.message : e.toString();
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(errorMessage),
        ));
      }
    });
  }
}
