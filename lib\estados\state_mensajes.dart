import 'package:flutter/material.dart';
import 'package:myrewards/dtos/mensaje.dart';
import 'package:myrewards/servicios/database/service_database.dart';
import 'package:myrewards/servicios/service_mensaje.dart';

class MensajesState with ChangeNotifier {
  List<Mensaje> _mensajes = [];
  int _totalMensajes = 0;
  bool _cargando = true;

  List<Mensaje> get mensajes => _mensajes;
  int get totalMensajes => _totalMensajes;
  bool get cargando => _cargando;

  misMensajes() async {
    _mensajes = await MensajeApi().misMensajes();
    _cargando = false;
    notifyListeners();
  }

  misMensajesContar() async {
    int totalServidor = await MensajeApi().misMensajesContar();
    int totalInterno = await DatabaseService.instance.contarNoLeidos();
    _totalMensajes = totalServidor + totalInterno;
    notifyListeners();
  }

  quitarMensaje(int id) {
    _mensajes = _mensajes.where((element) => element.id != id).toList();
    notifyListeners();
  }

  marcarComoLeido(int id) async {
    await MensajeApi().marcarComoLeido(id);
    _mensajes.firstWhere((element) => element.id == id).estado = "VIEWED";
    _totalMensajes = await DatabaseService.instance.contarNoLeidos();
    notifyListeners();
  }

  setCargando() {
    _cargando = true;
  }
}
