// import 'dart:convert';

// import 'package:myrewards/constantes/ambiente.dart';
// import 'package:package_info/package_info.dart';
// import 'package:http/http.dart' as http;

import 'dart:convert';
import 'dart:io';

import 'package:http/http.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:myrewards/constantes/ambiente.dart';
import 'package:myrewards/http/auth_interceptor.dart';
import 'package:myrewards/http/retry_policy.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppApi {
  Client client = InterceptedClient.build(
      interceptors: [LoggingInterceptor()],
      retryPolicy: ExpiredTokenRetryPolicy());

  static final AppApi _singleton = AppApi._internal();

  factory AppApi() {
    return _singleton;
  }

  AppApi._internal();

  Future<bool> verificarActualizacion() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();

      final response = await client.get(Uri.parse(
          '${Ambiente.urlApp}/version/${Platform.isIOS ? 'ios' : 'android'}'));

      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        if (data['version'] != '') {
          return packageInfo.version != data['version'];
        }
      }
    } catch (e) {
      return false;
    }
    return false;
  }
}
