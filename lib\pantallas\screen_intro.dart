import 'package:flutter/material.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:go_router/go_router.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shared_preferences/shared_preferences.dart';

class IntroScreen extends StatefulWidget {
  const IntroScreen({super.key});

  @override
  State<IntroScreen> createState() => _IntroScreenState();
}

class _IntroScreenState extends State<IntroScreen> {
  int paginaActual = 0;
  final controller = PageController(initialPage: 0);

  @override
  void initState() {
    super.initState();
    if (mounted) {
      marcarPrimeraVez();
    }
  }

  marcarPrimeraVez() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('primeraVez', false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: ColoresApp.acento,
        body: Safe<PERSON><PERSON>(
          child: <PERSON>ack(children: [
            Positioned(
              left: 0,
              top: 0,
              child: SvgPicture.asset(
                'assets/images/intro_bg_top_left.svg',
                height: MediaQuery.of(context).size.height * 0.5,
              ),
            ),
            Positioned(
              right: 0,
              bottom: 0,
              child: SvgPicture.asset('assets/images/intro_bg_bottom_right.svg',
                  height: MediaQuery.of(context).size.height * 0.5),
            ),
            NotificationListener<ScrollNotification>(
              onNotification: (ScrollNotification notification) {
                if (notification is OverscrollNotification) {
                  if (paginaActual == 2 && notification.overscroll > 0) {
                    context.go('/ingreso');
                  }
                }
                return false;
              },
              child: PageView(
                  onPageChanged: (page) async {
                    if (page == 1) {
                      var status = await Permission.camera.status;
                      if (status.isDenied) {
                        await Permission.camera.request();
                      }
                    } else if (page == 2) {
                      FirebaseMessaging messaging = FirebaseMessaging.instance;

                      NotificationSettings settings =
                          await messaging.requestPermission(
                        alert: true,
                        announcement: false,
                        badge: true,
                        carPlay: false,
                        criticalAlert: false,
                        provisional: false,
                        sound: true,
                      );

                      if (settings.authorizationStatus !=
                              AuthorizationStatus.authorized &&
                          settings.authorizationStatus !=
                              AuthorizationStatus.provisional) {
                        await FirebaseMessaging.instance.requestPermission();
                      }
                    }

                    setState(() {
                      paginaActual = page;
                    });
                  },
                  controller: controller,
                  children: [
                    SizedBox(
                        child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Text(
                                    "¿Que es MyReward?",
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 42),
                                  ),
                                  const SizedBox(height: 16),
                                  SvgPicture.asset(
                                      'assets/images/intro_que_es.svg'),
                                  const SizedBox(height: 32),
                                  const Text(
                                      "Tú tarjeta digital de recompensas",
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 28)),
                                ]))),
                    SizedBox(
                        child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    height: MediaQuery.of(context).size.height *
                                        0.45,
                                    child: SvgPicture.asset(
                                        'assets/images/intro_como_funciona.svg'),
                                  ),
                                  const SizedBox(height: 16),
                                  const Text(
                                    "¿Como funciona?",
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 42),
                                  ),
                                  const SizedBox(height: 24),
                                  const Text(
                                      "Junta estampillas MyReward en cada compra o visita a las tiendas asociadas",
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 21)),
                                ]))),
                    SizedBox(
                        child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Text(
                                    "¡Gana recompensas!",
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 42),
                                  ),
                                  const SizedBox(height: 16),
                                  SvgPicture.asset(
                                      'assets/images/intro_gana.svg'),
                                  const SizedBox(height: 32),
                                  const Text(
                                      "Al completar tu tarjeta, ganarás un cupón de recompensa",
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 28)),
                                ])))
                  ]),
            ),
            Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: SmoothPageIndicator(
                      controller: controller,
                      count: 3,
                      effect: const WormEffect(
                          dotColor: ColoresApp.segundario,
                          activeDotColor: ColoresApp.primario)),
                )),
            Align(
                alignment: Alignment.bottomRight,
                child: Visibility(
                    visible: paginaActual == 2,
                    child: Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: TextButton(
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.white,
                          ),
                          onPressed: () {
                            context.go('/ingreso');
                          },
                          child: const Text('Terminar'),
                        )))),
            Align(
                alignment: Alignment.bottomRight,
                child: Visibility(
                    visible: paginaActual != 2,
                    child: Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: TextButton(
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.white,
                            ),
                            onPressed: () {
                              controller.nextPage(
                                  duration: const Duration(milliseconds: 500),
                                  curve: Curves.easeInOut);
                            },
                            child: const Text('Siguiente')))))
          ]),
        ));
  }
}
