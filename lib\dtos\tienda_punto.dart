class TiendaPunto {
  final String nombre;
  final String codigo;
  final double latitud;
  final double longitud;

  const T<PERSON><PERSON><PERSON>unt<PERSON>(
      {required this.nombre,
      required this.codigo,
      required this.latitud,
      required this.longitud});

  static TiendaPunto fromJson(Map<String, dynamic> json) => TiendaPunto(
      codigo: json['code'],
      nombre: json['name'],
      latitud: json['latitude'],
      longitud: json['longitude']);
}
