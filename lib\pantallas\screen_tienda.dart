import 'package:flutter/material.dart';
import 'package:myrewards/componentes/widget_redes_sociales.dart';
import 'package:myrewards/constantes/ambiente.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:myrewards/dtos/tienda_detalle.dart';
import 'package:myrewards/servicios/service_tienda.dart';

class TiendaScreen extends StatefulWidget {
  final String shoppingCode;
  const TiendaScreen({super.key, required this.shoppingCode});

  @override
  State<TiendaScreen> createState() => _TiendaScreenState();
}

class _TiendaScreenState extends State<TiendaScreen> {
  TiendaDetalle? tiendaDetalle;
  bool cargando = true;

  void buscarDetalleTienda() {
    TiendaApi().tiendaDetalle(widget.shoppingCode).then((tienda) {
      if (tienda != null) {
        tiendaDetalle = tienda;
        cargando = false;
        setState(() {});
      }
    });
  }

  @override
  void initState() {
    super.initState();
    buscarDetalleTienda();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        foregroundColor: ColoresApp.texto,
        backgroundColor: Colors.transparent,
        elevation: 0,
        title:
            const Text("Tienda", style: TextStyle(fontWeight: FontWeight.bold)),
      ),
      body: cargando
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
            child: Column(
                children: [
                  Center(
                    child: SizedBox(
                      width: 100,
                      height: 100,
                      child: ClipOval(
                        child: Image.network(
                          "${Ambiente.urlImagenes}/shoppings/${tiendaDetalle!.imagen}",
                          fit: BoxFit.cover,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return const Center(
                              child:
                                  CircularProgressIndicator(color: Colors.white),
                            );
                          },
                          errorBuilder: (context, error, stackTrace) =>
                              const Icon(
                            Icons.store,
                            color: Colors.white,
                            size: 40,
                          ),
                        ),
                      ),
                    ),
                  ),
                  
            
                  Padding(
                    padding: const EdgeInsets.only(top: 20),
                    child: Text(
                      tiendaDetalle?.nombre ?? 'TIENDA',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  RedesSocialesWidget(codigoTienda: widget.shoppingCode),
                  _buildInfoSection(),
                ],
              ),
          ),
    );
  }

  Widget _buildInfoSection() {
    return Padding(
      padding: const EdgeInsets.only(left: 20, right: 20, bottom: 40),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          _buildInfoItem(
              Icons.description, 'Descripción:', tiendaDetalle?.descripcion),
          const SizedBox(height: 20),
          _buildInfoItem(Icons.phone, 'Teléfono:', tiendaDetalle?.telefono),
          const SizedBox(height: 20),
          _buildInfoMapItem(
              Icons.location_on, 'Dirección:', tiendaDetalle?.direccion),
        ],
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String title, String? value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: Colors.grey[600], size: 24),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value ?? 'Información no disponible',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInfoMapItem(IconData icon, String title, String? value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: Colors.grey[600], size: 24),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value ?? 'Información no disponible',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  height: 1.4,
                ),
              ),
              ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: Container(
                    color: Colors.grey[200],
                    height: 210.0,
                    width: double.infinity,
                    child: GoogleMap(
                      zoomControlsEnabled: false,
                      initialCameraPosition: CameraPosition(
                        target: LatLng(
                            tiendaDetalle!.latitud, tiendaDetalle!.longitud),
                        zoom: 16.0,
                      ),
                      markers: {
                        Marker(
                          markerId: const MarkerId('tienda'),
                          position: LatLng(
                              tiendaDetalle!.latitud, tiendaDetalle!.longitud),
                        ),
                      },
                    ),
                  ))
            ],
          ),
        ),
      ],
    );
  }
}
