import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:myrewards/componentes/dialog_tienda_info.dart';
import 'package:myrewards/constantes/ambiente.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:myrewards/dtos/tarjeta.dart';
import 'package:myrewards/dtos/tienda_categoria.dart';
import 'package:myrewards/estados/state_mensajes.dart';
import 'package:myrewards/estados/state_tarjeta.dart';
import 'package:myrewards/estados/state_tarjetas.dart';
import 'package:myrewards/estados/state_tiendas_destacadas.dart';
import 'package:myrewards/pantallas/inicio/widgets/widget_destacados.dart';
import 'package:myrewards/servicios/service_tienda.dart';
import 'package:myrewards/servicios/service_usuario.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:badges/badges.dart' as badges;

class InicioScreen extends StatefulWidget {
  const InicioScreen({super.key});

  @override
  State<InicioScreen> createState() => _InicioScreenState();
}

class _InicioScreenState extends State<InicioScreen> {
  

  int currentIndex = 0;

  String opcionSeleccionada = "ALL";
  int totalMensajes = 0;

  void obtenerTokenFcm() async {
    String? token = await FirebaseMessaging.instance.getToken();
    if (token != null) {
      UsuarioApi().actualizarToken(token).then((value) => {});
    }
  }

  Future<void> misTarjetas() async {
    context.read<TarjetasState>().misTarjetas();
  }

  void destacados() async {
    context.read<TiendasDestacadasState>().actualizarDestacados();
  }

  double clacularPorcentaje(int progreso, int total) {
    return (progreso / total).clamp(0.0, 1.0);
  }

  Future<void> _cargarTotalMensajes() async {
    context.read<MensajesState>().misMensajesContar();
  }

  @override
  void initState() {
    super.initState();
    obtenerTokenFcm();
    misTarjetas();
    _cargarTotalMensajes();
  }

  Future<void> buscarInicio() async {
    misTarjetas();
    destacados();
  }

  confirmar(String codigo) async {
    Navigator.of(context).pop();
    showDialog(
        context: context,
        builder: (context) {
          return TiendaInfoDialog(tiendaCodigo: codigo);
        });
  }

 

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
            foregroundColor: Colors.black,
            backgroundColor: ColoresApp.fondo,
            centerTitle: false,
            elevation: 0,
            title: Padding(
              padding: const EdgeInsets.only(right: 0.0),
              child: SvgPicture.asset(
                'assets/images/my_reward_vertical_m.svg',
                height: 35,
              ),
            ),
            actions: [
              badges.Badge(
                position: badges.BadgePosition.topEnd(top: 0, end: 3),
                showBadge: context.watch<MensajesState>().totalMensajes > 0,
                badgeContent: Text(
                  '${context.watch<MensajesState>().totalMensajes}',
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                ),
                badgeStyle: const badges.BadgeStyle(
                  badgeColor: Colors.red,
                  padding: EdgeInsets.all(6.0),
                ),
                child: IconButton(
                  icon: const Icon(Icons.mail_outline),
                  onPressed: () {
                    context.push("/mensajes");
                  },
                ),
              ),
            ]),
        body: RefreshIndicator(
          onRefresh: buscarInicio,
          child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (context
                        .watch<TiendasDestacadasState>()
                        .tiendasDestacadas
                        .isNotEmpty)
                      const DescatadosWidget(),
                    const Padding(
                      padding: EdgeInsets.only(top: 8, left: 16, right: 16),
                      child: Text("Tarjetas",
                          style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: ColoresApp.texto)),
                    ),
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.only(left: 16),
                      child: FutureBuilder<List<TiendaCategoria>>(
                          future: TiendaApi().obtenerCategorias(),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              final opciones = snapshot.data!;

                              return Container(
                                color: Colors.transparent,
                                margin: const EdgeInsets.only(right: 8),
                                height:
                                    40, // Altura del container que contendrá los chips
                                child: ListView.builder(
                                  scrollDirection:
                                      Axis.horizontal, // Scroll horizontal
                                  itemCount: opciones.length,
                                  itemBuilder: (context, index) {
                                    String opcion = opciones[index].codigo;
                                    bool isSelected =
                                        opcionSeleccionada == opcion;

                                    return Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 4.0),
                                      child: GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            if (opcion == opcionSeleccionada) {
                                              opcionSeleccionada = "ALL";
                                            } else {
                                              opcionSeleccionada = opcion;
                                            }

                                            context
                                                .read<TarjetasState>()
                                                .filtrarTarjetas(
                                                    opcionSeleccionada);
                                          });
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 21, vertical: 4),
                                          decoration: BoxDecoration(
                                            color: isSelected
                                                ? ColoresApp.amarillo
                                                : Colors
                                                    .white, // Amarillo si está activado, gris si no
                                            borderRadius: BorderRadius.circular(
                                                32), // Bordes redondeados para el estilo chip
                                          ),
                                          child: Center(
                                            child: Text(
                                              opciones[index].descripcion,
                                              style: TextStyle(
                                                color: isSelected
                                                    ? Colors.black
                                                    : Colors.grey[
                                                        700], // Texto negro si activado, gris oscuro si no
                                                fontWeight: FontWeight
                                                    .bold, // Negrita para que destaque más
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              );
                            } else {
                              return const SizedBox.shrink();
                            }
                          }),
                    ),
                    if (context.watch<TarjetasState>().cargando)
                      const AspectRatio(
                          aspectRatio: 16 / 9,
                          child: Center(child: CircularProgressIndicator())),
                    if (!context.watch<TarjetasState>().cargando &&
                        context.watch<TarjetasState>().tarjetas.isEmpty)
                      Padding(
                        padding: const EdgeInsets.only(
                            left: 16, right: 16, bottom: 32),
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              const SizedBox(height: 21),
                              SvgPicture.asset(
                                  'assets/images/sin_tarjetas.svg'),
                              const SizedBox(height: 16),
                              const Text("Ouch ! Aún no tienes tarjetas",
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: ColoresApp.acento)),
                              const SizedBox(height: 8),
                              const Text(
                                  "No te desanimes, revisa todos los locales adheridos a promociones y recompensas",
                                  style: TextStyle(color: ColoresApp.acento),
                                  textAlign: TextAlign.center)
                            ]),
                      ),
                    Padding(
                      padding: const EdgeInsets.only(
                          top: 8, left: 16, right: 16, bottom: 32),
                      child: Consumer<TarjetasState>(
                          builder: (context, tarjetasState, child) {
                        return ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: tarjetasState.tarjetas.length,
                            itemBuilder: (context, index) {
                              return GestureDetector(
                                  onTap: () {
                                    Tarjeta tarjeta = context
                                        .read<TarjetasState>()
                                        .buscarTarjeta(tarjetasState
                                            .tarjetas[index].codigo);
                                    context
                                        .read<TarjetaState>()
                                        .agregarTarjeta(tarjeta);
                                    context.push(
                                        "/tarjeta/${tarjetasState.tarjetas[index].codigo}/${tarjetasState.tarjetas[index].tiendaCodigo}");
                                  },
                                  child: Card(
                                      elevation: 6,
                                      margin: const EdgeInsets.only(
                                          left: 0, right: 0, bottom: 16),
                                      color: ColoresApp.acento,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: AspectRatio(
                                          aspectRatio: 16 / 9,
                                          child: Stack(children: [
                                            Positioned(
                                              right: 0,
                                              top: 0,
                                              child: SvgPicture.asset(
                                                  'assets/images/grupo_esquina.svg'),
                                            ),
                                            Positioned(
                                              left: 0,
                                              bottom: 0,
                                              child: SvgPicture.asset(
                                                  'assets/images/grupo_2_esquina.svg'),
                                            ),
                                            Container(
                                                color: Colors.transparent,
                                                child: Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            top: 16,
                                                            bottom: 16,
                                                            right: 8,
                                                            left: 21),
                                                    child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Row(children: [
                                                            Container(
                                                              decoration: BoxDecoration(
                                                                  color: Colors
                                                                      .white,
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              40)),
                                                              width: 70,
                                                              height: 70,
                                                              child: ClipRRect(
                                                                borderRadius:
                                                                    const BorderRadius
                                                                        .all(
                                                                        Radius.circular(
                                                                            40)),
                                                                child: FittedBox(
                                                                    fit: BoxFit
                                                                        .contain,
                                                                    child: Image
                                                                        .network(
                                                                            "${Ambiente.urlImagenes}/shoppings/${tarjetasState.tarjetas[index].tiendaLogo}")),
                                                              ),
                                                            ),
                                                            const SizedBox(
                                                                width: 8),
                                                            Expanded(
                                                                child: SizedBox(
                                                                    height: 70,
                                                                    child: Column(
                                                                        crossAxisAlignment:
                                                                            CrossAxisAlignment
                                                                                .start,
                                                                        mainAxisAlignment:
                                                                            MainAxisAlignment.start,
                                                                        children: [
                                                                          GestureDetector(
                                                                              onTap: () {
                                                                                context.push("/tienda/${tarjetasState.tarjetas[index].tiendaCodigo}");
                                                                              },
                                                                              child: Text(tarjetasState.tarjetas[index].tiendaNombre, style: const TextStyle(color: Colors.white, fontSize: 12, fontStyle: FontStyle.normal, decoration: TextDecoration.underline, decorationColor: Colors.white))),
                                                                          const SizedBox(
                                                                              height: 2),
                                                                          Text(
                                                                            tarjetasState.tarjetas[index].tituloRecompensa,
                                                                            style: const TextStyle(
                                                                                height: 1,
                                                                                color: Colors.white,
                                                                                fontWeight: FontWeight.bold,
                                                                                fontSize: 16),
                                                                          ),
                                                                          const SizedBox(
                                                                              height: 2),
                                                                          Text(
                                                                              "* ${tarjetasState.tarjetas[index].condicionCorta}",
                                                                              style: const TextStyle(fontSize: 10, color: Colors.white))
                                                                        ]))),
                                                            const Padding(
                                                              padding: EdgeInsets
                                                                  .only(
                                                                      right:
                                                                          16),
                                                              child: SizedBox(
                                                                  height: 60,
                                                                  child: Column(
                                                                      children: [
                                                                        Icon(
                                                                            Icons
                                                                                .shield,
                                                                            color:
                                                                                Colors.amber),
                                                                        SizedBox(
                                                                            height:
                                                                                4),
                                                                      ])),
                                                            )
                                                          ]),
                                                          const SizedBox(
                                                              height: 15),
                                                          Container(
                                                            color: Colors
                                                                .transparent,
                                                            width:
                                                                double.infinity,
                                                            child: Row(
                                                                children: [
                                                                  Text(
                                                                    "${tarjetasState.tarjetas[index].hitsUsuario}/${tarjetasState.tarjetas[index].hits}",
                                                                    style: const TextStyle(
                                                                        color: Colors
                                                                            .white,
                                                                        fontSize:
                                                                            15),
                                                                  ),
                                                                  const SizedBox(
                                                                      width: 8),
                                                                  Expanded(
                                                                      child:
                                                                          Container(
                                                                    height: 10,
                                                                    decoration: BoxDecoration(
                                                                        color: Colors
                                                                            .white,
                                                                        borderRadius:
                                                                            BorderRadius.circular(10)),
                                                                    child:
                                                                        Stack(
                                                                      children: [
                                                                        FractionallySizedBox(
                                                                          widthFactor: clacularPorcentaje(
                                                                              tarjetasState.tarjetas[index].hitsUsuario,
                                                                              tarjetasState.tarjetas[index].hits), // Ajusta la barra verde al porcentaje de carga
                                                                          child:
                                                                              Container(
                                                                            decoration:
                                                                                BoxDecoration(
                                                                              color: ColoresApp.verdeBarra, // Color de la barra de progreso
                                                                              borderRadius: BorderRadius.circular(10), // Bordes redondeados
                                                                            ),
                                                                          ),
                                                                        )
                                                                      ],
                                                                    ),
                                                                  )),
                                                                  const SizedBox(
                                                                      width: 8),
                                                                  Container(
                                                                    padding: const EdgeInsets
                                                                        .all(
                                                                        8), // Espaciado interno igual al ElevatedButton
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      color: Colors
                                                                          .white, // Fondo del botón
                                                                      shape: BoxShape
                                                                          .circle, // Forma circular
                                                                      boxShadow: [
                                                                        BoxShadow(
                                                                          color: Colors
                                                                              .black
                                                                              .withOpacity(0.3), // Color de la sombra con opacidad
                                                                          spreadRadius:
                                                                              2, // Expansión de la sombra
                                                                          blurRadius:
                                                                              4, // Difuminado de la sombra
                                                                          offset: const Offset(
                                                                              0,
                                                                              2), // Desplazamiento de la sombra en el eje X y Y
                                                                        ),
                                                                      ],
                                                                    ),
                                                                    child:
                                                                        const Icon(
                                                                      Icons
                                                                          .qr_code_2,
                                                                      color: ColoresApp
                                                                          .acento, // Color del ícono
                                                                    ),
                                                                  ),
                                                                  const SizedBox(
                                                                      width: 8)
                                                                ]),
                                                          ),
                                                          const SizedBox(
                                                              height: 8),
                                                          Align(
                                                              alignment: Alignment
                                                                  .bottomRight,
                                                              child: Row(
                                                                  crossAxisAlignment:
                                                                      CrossAxisAlignment
                                                                          .end,
                                                                  mainAxisAlignment:
                                                                      MainAxisAlignment
                                                                          .start,
                                                                  children: [
                                                                    const Text(
                                                                        "Válido hasta : ",
                                                                        style: TextStyle(
                                                                            fontSize:
                                                                                8,
                                                                            color:
                                                                                Colors.white)),
                                                                    const SizedBox(
                                                                        width:
                                                                            4),
                                                                    Text(
                                                                        DateFormat('dd-MM-yyyy').format(tarjetasState
                                                                            .tarjetas[
                                                                                index]
                                                                            .recompensaValidaHasta),
                                                                        style: const TextStyle(
                                                                            fontSize:
                                                                                8,
                                                                            color:
                                                                                Colors.white,
                                                                            fontWeight: FontWeight.bold))
                                                                  ]))
                                                        ])))
                                          ]))));
                            });
                      }),
                    ),
                  ])),
        ));
  }
}
