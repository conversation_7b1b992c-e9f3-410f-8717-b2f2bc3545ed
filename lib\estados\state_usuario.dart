import 'package:flutter/material.dart';
import 'package:myrewards/dtos/usuario.dart';
import 'package:myrewards/servicios/service_usuario.dart';

class UsuarioState with ChangeNotifier {
  Usuario? _usuario;

  Usuario? get usuario => _usuario;

  buscarUsuario() async {
    _usuario = await UsuarioApi().obtenerUsuario();
    notifyListeners();
  }

  setUsuario(Usuario usuario){
    _usuario = usuario;
    notifyListeners();
  }

  setFechaNacimiento(String fechaNacimiento){
    if(_usuario != null){
      _usuario!.fechaNacimiento = fechaNacimiento;
    notifyListeners();
    }
  }
}
