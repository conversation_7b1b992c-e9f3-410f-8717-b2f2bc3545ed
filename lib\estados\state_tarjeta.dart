import 'package:flutter/material.dart';
import 'package:myrewards/dtos/tarjeta.dart';

class TarjetaState with ChangeNotifier {
  Tarjeta? _tarjeta;

  set tarjeta(value) => _tarjeta;

  Tarjeta? get tarjeta => _tarjeta;

  actualizarEstampillas(int cantidad) {
    if (_tarjeta != null) {
      if (_tarjeta!.hitsUsuario != cantidad) {
        _tarjeta!.hitsUsuario = cantidad;
        notifyListeners();
      }
    }
  }

  agregarTarjeta(Tarjeta tarjeta) {
    _tarjeta = tarjeta;
  }
}
