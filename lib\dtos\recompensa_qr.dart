class RecompensaQr {
  final String codigo;
  final String recompensaTitulo;
  final String tiendaNombre;

  const RecompensaQr(
      {required this.codigo,
      required this.recompensaTitulo,
      required this.tiendaNombre});

  static RecompensaQr fromJson(Map<String, dynamic> json) => RecompensaQr(
      codigo: json['code'],
      recompensaTitulo: json['rewardTitle'],
      tiendaNombre: json['shoppingName']);
}
