import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:myrewards/componentes/bottom_nav_app.dart';
import 'package:myrewards/componentes/dialog_alerta.dart';
import 'package:myrewards/componentes/dialog_tienda_info.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:myrewards/estados/state_cupones.dart';
import 'package:myrewards/estados/state_mensajes.dart';
import 'package:myrewards/estados/state_tarjeta.dart';
import 'package:myrewards/estados/state_tarjetas.dart';
import 'package:myrewards/estados/state_tiendas_destacadas.dart';
import 'package:myrewards/pantallas/inicio/widgets/widget_recompoensa_animation.dart';
import 'package:myrewards/pantallas/screen_cupones.dart';
import 'package:myrewards/pantallas/inicio/screen_inicio.dart';
import 'package:myrewards/pantallas/screen_map.dart';
import 'package:myrewards/pantallas/screen_usuario.dart';
import 'package:myrewards/servicios/exception/api_exception.dart';
import 'package:myrewards/servicios/service_recompensa.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart';
import 'dart:async';

class MarcoScreen extends StatefulWidget {
  final String? shoppingCode;
  final String? recompensaQrCodigo;
  final String? timestamp;
  const MarcoScreen(
      {super.key, this.shoppingCode, this.recompensaQrCodigo, this.timestamp});

  @override
  State<MarcoScreen> createState() => _MarcoScreenState();
}

class _MarcoScreenState extends State<MarcoScreen>
    with SingleTickerProviderStateMixin {
  int indiceActual = 0;
  StreamSubscription? _sub;

  final pantallas = [
    const InicioScreen(),
    const MapaScreen(),
    const SizedBox.shrink(),
    const CuponesScreen(),
    const UsuarioScreen()
  ];

  void handleMessage(RemoteMessage? message) {
    if (!mounted) return;
    if (message == null) return;
    String tipoMensaje = message.data['push_type'];
    if (tipoMensaje == 'card_hit') {
      context
          .read<TarjetaState>()
          .actualizarEstampillas(int.parse(message.data['card_hit_count']));

      context.read<TarjetasState>().actualizarHits(
          message.data['card_code'], int.parse(message.data['card_hit_count']));

      if (message.data['is_last'] == 'T') {
        context.read<TarjetasState>().quitarTarjeta(message.data['card_code']);
        context.read<TiendasDestacadasState>().actualizarDestacados();
        context.read<CuponesState>().misCupones();
        context.read<CuponesState>().nuevoCupon = true;
        showDialog(
            context: context,
            builder: (context) {
              return AlertaDialog(
                  mensaje:
                      "¡¡ Felicidades !!, te has ganado el siguiente cupón: ${message.data['reward_name']}");
            }).then((value) {
          if (GoRouter.of(context)
              .routerDelegate
              .currentConfiguration
              .last
              .matchedLocation
              .startsWith("/tarjeta")) {
            Future.delayed(const Duration(milliseconds: 500)).then((value) {
              Navigator.of(context).pop();
            });
          }

          Future.delayed(const Duration(milliseconds: 800)).then((value) {
            showRewardAnimation(context);
          });
        });
      }
    } else if (tipoMensaje == 'reward_used') {
      context.read<CuponesState>().misCupones();
      showDialog(
          context: context,
          builder: (context) {
            return AlertaDialog(
                mensaje:
                    "¡¡ Felicidades !!, canjeaste tu recompensa : ${message.data['reward_name']}");
          }).then((value) {
        if (GoRouter.of(context)
            .routerDelegate
            .currentConfiguration
            .last
            .matchedLocation
            .startsWith("/cupon")) {
          Future.delayed(const Duration(milliseconds: 500))
              .then((value) => Navigator.of(context).pop());
        }
      });
    } else if (tipoMensaje == 'client_reward') {
      context.read<CuponesState>().misCupones();
      context.read<CuponesState>().nuevoCupon = true;
      showDialog(
          context: context,
          builder: (context) {
            return AlertaDialog(
                mensaje:
                    "¡¡ Felicidades !!, te haz ganado el siguiente cupón: ${message.data['reward_name']}");
          }).then((value) {
        if (GoRouter.of(context)
            .routerDelegate
            .currentConfiguration
            .last
            .matchedLocation
            .startsWith("/tarjeta")) {
          Future.delayed(const Duration(milliseconds: 500))
              .then((value) => Navigator.of(context).pop());
        }
      });
    } else if (tipoMensaje == 'message') {
      context.read<MensajesState>().misMensajesContar();

      showDialog(
          context: context,
          builder: (context) {
            return const AlertaDialog(
                mensaje:
                    "Has recibido un nuevo mensaje, revisa tu buzón de mensajes");
          });
    }
  }

  void showRewardAnimation(BuildContext context) {
    final overlay = Overlay.of(context);
    final renderBox =
        context.findRenderObject() as RenderBox?; // Usar operador null-safe

    if (renderBox == null) return; // Validación adicional

    // Guardar referencia al OverlayEntry para poder eliminarlo después
    final entry = OverlayEntry(
      builder: (context) => const RewardLottieAnimation(
        duration: Duration(seconds: 2),
      ),
    );

    overlay.insert(entry);

    // Eliminar el overlay después de que termine la animación
    Future.delayed(const Duration(seconds: 2), () {
      entry.remove();
    });
  }

  @override
  void didUpdateWidget(MarcoScreen oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.timestamp != widget.timestamp) {
      _handleIncomingCode();
    }
  }

  void buscarRecompensa(String recompensaCodigo) {
    RecompensasApi().aceptarRecompensaPorCodigo(recompensaCodigo).then((value) {
      context.read<CuponesState>().misCupones();
      context.read<CuponesState>().nuevoCupon = true;
      showDialog(
          context: context,
          builder: (context) {
            return AlertaDialog(
                mensaje:
                    "¡¡ Felicidades !!, te has ganado el siguiente cupón: ${value!.recompensaTitulo}");
          }).then((value) {
        Future.delayed(const Duration(milliseconds: 800)).then((value) {
          showRewardAnimation(context);
        });
      });
    }, onError: (e) {
      if (mounted) {
        String errorMessage;

        if (e is ApiException) {
          errorMessage = e.message;
        } else {
          errorMessage = e.toString();
        }

        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(errorMessage),
        ));
      }
    });
  }

  @override
  void initState() {
    super.initState();
    FirebaseMessaging.onMessageOpenedApp.listen(handleMessage);
    FirebaseMessaging.onMessage.listen(handleMessage);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _handleIncomingCode();
    });
  }

  void _handleIncomingCode() {
    if (widget.shoppingCode != null) {
      showDialog(
          context: context,
          builder: (context) {
            return TiendaInfoDialog(
              tiendaCodigo: widget.shoppingCode!,
            );
          });
    }

    if (widget.recompensaQrCodigo != null) {
      buscarRecompensa(widget.recompensaQrCodigo!);
    }
  }

  @override
  void dispose() {
    _sub?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(
          children: [
            IndexedStack(
              index: indiceActual,
              children: pantallas,
            )
          ],
        ),
        bottomNavigationBar: AppBottomNav(
            initIndex: indiceActual,
            indexChange: (i) {
              setState(() {
                indiceActual = i == 2 ? indiceActual : i;
              });
              if (i == 3) {
                context.read<CuponesState>().nuevoCupon = false;
              }
            }),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: MediaQuery.of(context).viewInsets.bottom > 0
            ? null // No mostrar el botón cuando el teclado está visible
            : FloatingActionButton(
                elevation: 0,
                shape: RoundedRectangleBorder(
                    side: const BorderSide(
                        width: 4, color: Colors.white, strokeAlign: 0),
                    borderRadius: BorderRadius.circular(16)),
                backgroundColor: ColoresApp.acento,
                tooltip: 'Increment',
                onPressed: () {
                  context.push('/lectura');
                },
                child: Container(
                  color: Colors.transparent,
                  width: 70,
                  height: 70,
                  child: Lottie.asset('assets/images/qr_scan.json',
                      width: 100, height: 100, repeat: true, fit: BoxFit.cover),
                ),
              ));
  }

  // void _showUpdateDialog() {
  //   showDialog(
  //     context: context,
  //     barrierDismissible:
  //         false, // Evita que el usuario cierre el diálogo sin actualizar
  //     builder: (BuildContext context) {
  //       return AlertDialog(
  //         title: const Text('Actualización disponible'),
  //         content: const Text(
  //             'Hay una nueva versión disponible. Por favor, actualiza la aplicación.'),
  //         actions: <Widget>[
  //           ElevatedButton(
  //             onPressed: _launchURL,
  //             child: const Text('Actualizar'),
  //           ),
  //         ],
  //       );
  //     },
  //   );
  // }

  // void _launchURL() async {
  //   var url = Platform.isIOS
  //       ? 'https://apps.apple.com/app/myreward-escanea-y-gana/id6608960895'
  //       : 'https://play.google.com/store/apps/details?id=cl.creabits.my.reward';
  //   if (await canLaunchUrl(Uri.parse(url))) {
  //     await launchUrl(Uri.parse(url));
  //   } else {
  //     throw 'No se pudo abrir la tienda';
  //   }
  // }
}
