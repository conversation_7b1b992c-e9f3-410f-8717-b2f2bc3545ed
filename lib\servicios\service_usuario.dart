import 'dart:convert';

import 'package:http/http.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:myrewards/constantes/ambiente.dart';
import 'package:myrewards/dtos/usuario.dart';
import 'package:myrewards/http/auth_interceptor.dart';
import 'package:myrewards/http/retry_policy.dart';
import 'package:http/http.dart' as http;

class UsuarioApi {
  Client client = InterceptedClient.build(
      interceptors: [LoggingInterceptor()],
      retryPolicy: ExpiredTokenRetryPolicy());

  static final UsuarioApi _singleton = UsuarioApi._internal();

  factory UsuarioApi() {
    return _singleton;
  }

  UsuarioApi._internal();

  Future actualizarToken(String tokenFcm) async {
    try {
      final response = await client.patch(
        Uri.parse("${Ambiente.urlUsuario}/token/fcm"),
        body: '{ "tokenFcm": "$tokenFcm" }',
      );

      if (response.statusCode == 200) {
        return null;
      }
    } catch (e) {
      //controlar exception
    }
  }

  Future<Usuario?> obtenerUsuario() async {
    try {
      var response = await client.get(Uri.parse(Ambiente.urlUsuario));

      if (response.statusCode == 200) {
        return Usuario.fromJson(jsonDecode(utf8.decode(response.bodyBytes)));
      }
    } catch (e) {
      //controlar exception
    }

    return null;
  }

  Future<Usuario?> obtenerUsuarioRrSs(String rrssId) async {
    try {
      var response =
          await client.get(Uri.parse("${Ambiente.urlUsuario}/social/$rrssId"));

      if (response.statusCode == 200) {
        return Usuario.fromJson(jsonDecode(utf8.decode(response.bodyBytes)));
      }
    } catch (e) {
      //controlar exception
    }

    return null;
  }

  Future actualizarNombre(String nombre) async {
    try {
      final response = await client.patch(
        Uri.parse("${Ambiente.urlUsuario}/update"),
        body: '{ "name": "$nombre" }',
      );

      if (response.statusCode == 200) {
        return null;
      }
    } catch (e) {
      //controlar exception
    }
  }

  Future actualizarValor(String propiedad, String valor) async {
    try {
      final response = await client.patch(
        Uri.parse("${Ambiente.urlUsuario}/client/update"),
        body: '{ "$propiedad": "$valor" }',
      );

      if (response.statusCode == 200) {
        return null;
      }
    } catch (e) {
      //controlar exception
    }
  }

  Future guardarUbicacion(double latitud, double longitud) async {
    try {
      final response = await client.put(
        Uri.parse("${Ambiente.urlUsuario}/location"),
        body: '{ "latitude": $latitud, "longitude": $longitud }',
      );

      if (response.statusCode == 200) {
        return null;
      }
    } catch (e) {
      //controlar exception
    }
  }

  Future<dynamic> obtenerUbicacionPorIP() async {
    try {
      final url = Uri.parse("http://ip-api.com/json");
      final response = await http.get(url);

      if (response.statusCode == 200) {
        return json.decode(response.body);
        // String ciudad = data["city"];
        // String pais = data["country"];
        // double latitud = data["lat"];
        // double longitud = data["lon"];

        // print("Ubicación aproximada: $ciudad, $pais ($latitud, $longitud)");
      }
    } catch (e) {
      return null;
    }
  }
}
