import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:myrewards/componentes/text_form_field_app.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:myrewards/dtos/usuario.dart';
import 'package:myrewards/servicios/exception/api_exception.dart';
import 'package:myrewards/servicios/service_autorizacion.dart';
import 'package:myrewards/storage/secure_storage.dart';
import 'package:myrewards/util/cargando_util.dart';
//import 'package:url_launcher/url_launcher.dart';

class IngresoScreen extends StatefulWidget {
  final String? motivo;
  const IngresoScreen({super.key, this.motivo});

  @override
  State<IngresoScreen> createState() => _IngresoScreenState();
}

class _IngresoScreenState extends State<IngresoScreen> {
  final SecureStorage secureStorage = SecureStorage();
  final TextEditingController tecCorreo = TextEditingController();
  final TextEditingController tecClave = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  void continuar() {
    if (_formKey.currentState!.validate()) {
      CargandoUtil().cargando("Ingresando ... ", context);
      final usuario = Usuario(correo: tecCorreo.text, clave: tecClave.text);
      AuthApi().ingresar(usuario).then((token) {
        context.pop();
        secureStorage
            .writeSecureData("token", token)
            .then((value) => {context.go('/inicio')});
      }, onError: (e) {
        context.pop();
        String errorMessage;

        if (e is ApiException) {
          errorMessage = e.message;
        } else {
          errorMessage = e.toString();
        }

        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(errorMessage),
        ));
      });
    }
  }

  void continuarLoginSocial(Usuario usuario) {
    AuthApi().ingresarSocial(usuario).then((token) {
      context.pop();
      secureStorage
          .writeSecureData("token", token)
          .then((value) => {context.go('/inicio')});
    }, onError: (e) {
      context.pop();
      String errorMessage;

      if (e is ApiException) {
        errorMessage = e.message;
      } else {
        errorMessage = e.toString();
      }

      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text(errorMessage),
      ));
    });
  }

  void continuarFacebook() {
    CargandoUtil().cargando("Ingresando con Facebook ... ", context);
    AuthApi().registrarConFacebook().then((usuario) {
      continuarLoginSocial(usuario);
    }, onError: (e) {
      context.pop();
    });
  }

  void continuarGoogle() {
    CargandoUtil().cargando("Ingresando con Google ... ", context);
    AuthApi().registrarGoogle().then((usuario) {
      continuarLoginSocial(usuario);
    }, onError: (e) {
      context.pop();
    });
  }

  void continuarApple() {
    CargandoUtil().cargando("Ingresando con Apple ... ", context);
    AuthApi().registrarApple().then((usuario) {
      continuarLoginSocial(usuario);
    }, onError: (e) {
      context.pop();
    });
  }

  // cargando(String mensaje) {
  //   showDialog(
  //     context: context,
  //     barrierDismissible: false,
  //     builder: (BuildContext context) {
  //       return Dialog(
  //         child: Padding(
  //           padding: const EdgeInsets.all(16.0),
  //           child: Row(
  //             mainAxisSize: MainAxisSize.min,
  //             children: [
  //               const CircularProgressIndicator(),
  //               const SizedBox(width: 16),
  //               Text(mensaje),
  //             ],
  //           ),
  //         ),
  //       );
  //     },
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          title: const Text(
            "Ingresa con tu cuenta",
            style:
                TextStyle(color: ColoresApp.texto, fontWeight: FontWeight.bold),
          )),
      body: SingleChildScrollView(
        child: Padding(
          padding:
              const EdgeInsets.only(top: 21, left: 16, right: 16, bottom: 8),
          child: Form(
            key: _formKey,
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  TextFormFieldApp(
                      controller: tecCorreo,
                      label: "Correo electrónico",
                      hint: "Escribe tu correo acá",
                      keyboardType: TextInputType.emailAddress,
                      validatior: (value) {
                        if (value == null || value.isEmpty) {
                          return "Debe ingresar el correo electrónico";
                        }

                        bool emailValid = RegExp(
                                r"^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,253}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,253}[a-zA-Z0-9])?)*$")
                            .hasMatch(value);

                        if (!emailValid) {
                          return 'Correo electrónico inválido';
                        }
                        return null;
                      }),
                  const SizedBox(height: 8),
                  TextFormFieldApp(
                    controller: tecClave,
                    label: "Clave",
                    hint: "Escribe tu clave",
                    password: true,
                    textInputAction: TextInputAction.done,
                    validatior: (value) {
                      if (value == null || value.isEmpty) {
                        return "Debe ingresar su clave";
                      }
                      return null;
                    },
                    onFieldSubmitted: (_) => continuar(),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                      width: double.infinity,
                      height: 55,
                      child: ElevatedButton(
                          onPressed: continuar,
                          style: ElevatedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                          child: const Text(
                            "Ingresar",
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold),
                          ))),
                  const SizedBox(height: 16),
                  const Text(
                    "¿No tienes cuenta?",
                    style: TextStyle(color: ColoresApp.texto),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                      width: double.infinity,
                      height: 55,
                      child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white, // Color de fondo
                            foregroundColor:
                                ColoresApp.texto, // Color del texto
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                  20), // Bordes redondeados
                              side: const BorderSide(
                                  color: ColoresApp
                                      .acento), // Borde con color personalizado
                            ),
                          ),
                          onPressed: () {
                            context.push('/registro');
                          },
                          child: const Text(
                            "Registrate en MyReward",
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold),
                          ))),
                  const SizedBox(height: 16),
                  const Text(
                    "o ingresa con",
                    style: TextStyle(color: ColoresApp.texto),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                            backgroundColor: ColoresApp.segundario,
                            padding: EdgeInsets.zero,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                  16), // Bordes redondeados de 8 unidades
                            ),
                            fixedSize: const Size(50, 50)),
                        onPressed: continuarGoogle,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: SvgPicture.asset(
                            'assets/images/google.svg',
                            height: 35.0,
                            width: 35.0,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                            backgroundColor: ColoresApp.segundario,
                            padding: EdgeInsets.zero,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                  16), // Bordes redondeados de 8 unidades
                            ),
                            fixedSize: const Size(50, 50)),
                        onPressed: continuarFacebook,
                        child: SvgPicture.asset(
                          'assets/images/facebook.svg',
                          height: 35.0,
                          width: 35.0,
                        ),
                      ),
                      if (Platform.isIOS) const SizedBox(width: 16),
                      if (Platform.isIOS)
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              backgroundColor: ColoresApp.segundario,
                              padding: EdgeInsets.zero,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(
                                    16), // Bordes redondeados de 8 unidades
                              ),
                              fixedSize: const Size(50, 50)),
                          onPressed: continuarApple,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: SvgPicture.asset(
                              'assets/images/apple.svg',
                              height: 35.0,
                              width: 35.0,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 42),
                  RichText(
                    textAlign: TextAlign.center,
                    text: TextSpan(
                      children: <TextSpan>[
                        const TextSpan(
                          text:
                              'Al ingresar o registrarte en MyReward estás aceptando los ',
                          style: TextStyle(color: ColoresApp.texto),
                        ),
                        TextSpan(
                          text: 'Términos y Condiciones',
                          style: const TextStyle(
                              color: ColoresApp.acento,
                              decoration: TextDecoration.underline,
                              fontWeight: FontWeight.bold),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () => {}//launchUrl(Uri.parse('https://myreward.cl/legal')),
                        )
                      ],
                    ),
                  )
                ]),
          ),
        ),
      ),
    );
  }
}
