import 'dart:async';
import 'dart:convert';

import 'package:http/http.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:myrewards/constantes/ambiente.dart';
import 'package:myrewards/dtos/comunes/paginado.dart';
import 'package:myrewards/dtos/tarjeta.dart';
import 'package:myrewards/http/auth_interceptor.dart';
import 'package:myrewards/http/retry_policy.dart';
import 'package:myrewards/servicios/exception/api_exception.dart';

class TarjetaApi {
  Client client = InterceptedClient.build(
      interceptors: [LoggingInterceptor()],
      retryPolicy: ExpiredTokenRetryPolicy());

  static final TarjetaApi _singleton = TarjetaApi._internal();

  factory TarjetaApi() {
    return _singleton;
  }

  TarjetaApi._internal();

  Future<List<Tarjeta>> misTarjetas() async {
    try {
      var response =
          await client.get(Uri.parse("${Ambiente.urlTarjeta}/client/all"));
      if (response.statusCode == 200) {
        final Paginado data =
            Paginado.fromJson(jsonDecode(utf8.decode(response.bodyBytes)));
        List<Tarjeta> datos = data.content.map((e) {
          return Tarjeta.fromJson(e);
        }).toList();
        return datos;
      }
    } catch (e) {
      //controlar exception
    }

    return [];
  }

  Future<Tarjeta?> asociarTarjeta(String codigo) async {
    try {
      final response = await client.post(
        Uri.parse("${Ambiente.urlTarjeta}/client/create"),
        body: '{ "shoppingCode": "$codigo" }',
      );

      if (response.statusCode == 201) {
        Tarjeta tar =
            Tarjeta.fromJson(jsonDecode(utf8.decode(response.bodyBytes)));
        return tar;
      } else {
        final errorResponse = json.decode(response.body);
        throw ApiException.fromJson(errorResponse);
      }
    } on TimeoutException {
      throw TimeoutException(
          'La solicitud a tardó demasiado, inténtalo nuevamente más tarde.');
    } on ApiException {
      rethrow;
    } catch (e) {
      throw Exception('Error inesperado, inténtalo en un momento');
    }
  }

  Future<int?> buscarHitsTarjeta(String codigo) async {
    try {
      var response = await client
          .get(Uri.parse("${Ambiente.urlTarjeta}/client/total/$codigo"));

      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        return data['userCardHits'];
      } else {
        final errorResponse = json.decode(response.body);
        throw ApiException.fromJson(errorResponse);
      }
    } on TimeoutException {
      throw TimeoutException(
          'La solicitud a tardó demasiado, inténtalo nuevamente más tarde.');
    } on ApiException {
      rethrow;
    } catch (e) {
      throw Exception('Error inesperado, inténtalo en un momento');
    }
  }

  Future<bool> borrarTarjeta(String codigo) async {
    try {
      var response = await client
          .delete(Uri.parse("${Ambiente.urlTarjeta}/client/$codigo"));
      if (response.statusCode == 200) {
        return true;
      } else {
        final errorResponse = json.decode(response.body);
        throw ApiException.fromJson(errorResponse);
      }
    } on TimeoutException {
      throw TimeoutException(
          'La solicitud a tardó demasiado, inténtalo nuevamente más tarde.');
    } on ApiException {
      rethrow;
    } catch (e) {
      throw Exception('Error inesperado, inténtalo en un momento');
    }
  }
}
