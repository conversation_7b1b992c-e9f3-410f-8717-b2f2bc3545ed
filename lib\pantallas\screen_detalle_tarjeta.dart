import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:myrewards/componentes/dialog_confirmacion.dart';
import 'package:myrewards/componentes/dialog_tienda_info.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:myrewards/dtos/tarjeta.dart';
import 'package:myrewards/estados/state_tarjeta.dart';
import 'package:myrewards/estados/state_tarjetas.dart';
import 'package:myrewards/estados/state_tiendas_destacadas.dart';
import 'package:myrewards/servicios/exception/api_exception.dart';
import 'package:myrewards/servicios/service_tarjeta.dart';
import 'package:myrewards/util/cargando_util.dart';
import 'package:myrewards/util/fecha_util.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
//import 'dart:developer';

class DetalleTarjetaScreen extends StatefulWidget {
  final String codigoTarjeta;
  final String codigoTienda;
  const DetalleTarjetaScreen(
      {super.key, required this.codigoTarjeta, required this.codigoTienda});

  @override
  State<DetalleTarjetaScreen> createState() => _DetalleTarjetaScreenState();
}

class _DetalleTarjetaScreenState extends State<DetalleTarjetaScreen> {
  late Tarjeta? tarjeta;
  late double tamanoEstampilla;

  void calcularTamanoEstampilla(int cantidad) {
    var ancho = MediaQuery.sizeOf(context).width;
    var margen = 48;
    if (cantidad == 1) {
      tamanoEstampilla = ancho;
    } else if (cantidad == 2 || cantidad == 4 || cantidad == 6) {
      tamanoEstampilla = (ancho / 2) - margen;
    } else if (cantidad == 3 || cantidad == 5 || cantidad >= 7) {
      tamanoEstampilla = (ancho / 3) - margen;
    }
  }

  @override
  void initState() {
    super.initState();
    tarjeta = context.read<TarjetaState>().tarjeta;
    //log(tarjeta!.recompensaValidaHasta.toString());
    verificarNotificacionesActivas();
  }

  Future<void> verificarNotificacionesActivas() async {
    var status = await Permission.notification.status;
    if (!status.isGranted) {
      await FirebaseMessaging.instance.requestPermission();
    }
  }

  void eliminarTarjeta() {
    CargandoUtil().cargando("Eliminando tarjeta ...", context);
    TarjetaApi().borrarTarjeta(widget.codigoTarjeta).then((valor) {
      if (mounted) {
        Navigator.of(context).pop();
        context.read<TarjetasState>().quitarTarjeta(widget.codigoTarjeta);
        context.read<TiendasDestacadasState>().actualizarDestacados();
        Navigator.of(context).pop();
      }
    }, onError: (e) {
      Navigator.of(context).pop();
      String errorMessage;

      if (e is ApiException) {
        errorMessage = e.message;
      } else {
        errorMessage = e.toString();
      }

      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text(errorMessage),
      ));
    });
  }

  Future<void> buscarHits() async {
    TarjetaApi().buscarHitsTarjeta(widget.codigoTarjeta).then((total) {
      context.read<TarjetaState>().actualizarEstampillas(total!);
    }, onError: (e) {
      String errorMessage;

      if (e is ApiException) {
        errorMessage = e.message;
      } else {
        errorMessage = e.toString();
      }

      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text(errorMessage),
      ));
    });
  }

  @override
  Widget build(BuildContext context) {
    calcularTamanoEstampilla(tarjeta!.hits);

    return Scaffold(
      appBar: AppBar(
        foregroundColor: ColoresApp.texto,
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: tarjeta == null
            ? null
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(tarjeta!.tiendaNombre,
                      style: const TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 2),
                  GestureDetector(
                    onTap: () {
                      context.push("/tienda/${widget.codigoTienda}");
                    },
                    child: const Text(
                      "Ver detalles",
                      style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.normal,
                          decoration: TextDecoration.underline),
                    ),
                  ),
                ],
              ),
        actions: [
          IconButton(
              onPressed: () {
                showDialog<String>(
                    context: context,
                    builder: (context) {
                      return const ConfirmacionDialog(
                          mensaje:
                              "¿Quieres eliminar la tarjeta?, perderás todo tu avance");
                    }).then((value) {
                  if (value == 'T') {
                    eliminarTarjeta();
                  }
                });
              },
              icon: SvgPicture.asset('assets/images/basura.svg',
                  height: 20, width: 20))
        ],
      ),
      body: tarjeta == null
          ? null
          : RefreshIndicator(
              onRefresh: buscarHits,
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        minHeight: constraints.maxHeight,
                      ),
                      child: IntrinsicHeight(
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            SizedBox(
                                height: constraints.maxHeight - 200,
                                width: double.infinity,
                                child: ConstrainedBox(
                                  constraints: BoxConstraints(
                                      minHeight: constraints.maxHeight - 200),
                                  child: IntrinsicHeight(
                                      child: Column(
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          showDialog(
                                              context: context,
                                              builder: (context) {
                                                return TiendaInfoDialog(
                                                  tiendaCodigo:
                                                      widget.codigoTienda,
                                                  mostrarAgregar: false,
                                                );
                                              });
                                        },
                                        child: SizedBox(
                                          height: 50,
                                          width: double.infinity,
                                          child: Center(
                                            child: Text(
                                              tarjeta!.tituloRecompensa,
                                              textAlign: TextAlign.center,
                                              style: const TextStyle(
                                                  color: ColoresApp.acento,
                                                  decoration:
                                                      TextDecoration.underline,
                                                  decorationColor:
                                                      ColoresApp.acento,
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 18),
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                          height: constraints.maxHeight - 300,
                                          width: double.infinity,
                                          child: Center(
                                              child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 16),
                                            child: SizedBox(
                                              child: Wrap(children: [
                                                for (int i = 1;
                                                    i <
                                                        (context
                                                                .watch<
                                                                    TarjetaState>()
                                                                .tarjeta!
                                                                .hits +
                                                            1);
                                                    i++)
                                                  Stack(
                                                    children: [
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .all(8.0),
                                                        child: SizedBox(
                                                          width:
                                                              tamanoEstampilla,
                                                          height: 70,
                                                          child: Center(
                                                              child: Stack(
                                                            children: [
                                                              Container(
                                                                  width: 70,
                                                                  height: 70,
                                                                  decoration: BoxDecoration(
                                                                      color: i <= context.watch<TarjetaState>().tarjeta!.hitsUsuario
                                                                          ? ColoresApp
                                                                              .acento
                                                                          : Colors
                                                                              .grey
                                                                              .shade300,
                                                                      border: Border.all(
                                                                          color: ColoresApp
                                                                              .segundario,
                                                                          width:
                                                                              1),
                                                                      shape: BoxShape
                                                                          .circle),
                                                                  child: Center(
                                                                      child:
                                                                          Text(
                                                                    (i).toString(),
                                                                    style: const TextStyle(
                                                                        color: Colors
                                                                            .white,
                                                                        fontSize:
                                                                            28,
                                                                        fontWeight:
                                                                            FontWeight.bold),
                                                                  ))),
                                                              if (i <=
                                                                  context
                                                                      .watch<
                                                                          TarjetaState>()
                                                                      .tarjeta!
                                                                      .hitsUsuario)
                                                                Positioned(
                                                                    top: 0,
                                                                    left: 0,
                                                                    child:
                                                                        Container(
                                                                      color: Colors
                                                                          .transparent,
                                                                      height:
                                                                          70,
                                                                      width: 70,
                                                                      child: Lottie.asset(
                                                                          'assets/images/scratch.json',
                                                                          repeat:
                                                                              false),
                                                                    ))
                                                            ],
                                                          )),
                                                        ),
                                                      )
                                                    ],
                                                  )
                                              ]),
                                            ),
                                          ))),
                                      SizedBox(
                                          height: 50,
                                          width: double.infinity,
                                          child: Center(
                                              child: Text(
                                            "Valido hasta ${DateFormat('dd-MM-yyyy').format(tarjeta!.recompensaValidaHasta)} (quedan ${FechaUtil().calcularDias(tarjeta!.recompensaValidaHasta)} días)",
                                            style: const TextStyle(
                                                color: ColoresApp.texto,
                                                fontSize: 12),
                                          ))),
                                    ],
                                  )),
                                )),
                            Container(
                                height: 200,
                                decoration: const BoxDecoration(
                                    color: ColoresApp.acento,
                                    borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(40),
                                        topRight: Radius.circular(40))),
                                child: Center(
                                    child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    QrImageView(
                                      data: widget.codigoTarjeta,
                                      version: QrVersions.auto,
                                      eyeStyle: const QrEyeStyle(
                                        eyeShape: QrEyeShape.square,
                                        color: Colors.white,
                                      ),
                                      dataModuleStyle: const QrDataModuleStyle(
                                        dataModuleShape: QrDataModuleShape.square,
                                        color: Colors.white
                                      ),
                                      size: 120.0,
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      widget.codigoTarjeta,
                                      style: const TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                          letterSpacing: 5),
                                    )
                                  ],
                                ))),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
    );
  }
}
