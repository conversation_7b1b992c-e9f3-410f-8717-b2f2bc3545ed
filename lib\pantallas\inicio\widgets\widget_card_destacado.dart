import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:myrewards/componentes/dialog_tienda_info.dart';
import 'package:myrewards/constantes/ambiente.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:myrewards/dtos/tienda_destacada.dart';
import 'package:go_router/go_router.dart';

class CardDestacadoWidget extends StatelessWidget {
  final TiendaDestacada tiendaDestacada;
  const CardDestacadoWidget({
    super.key, required this.tiendaDestacada,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: Container(
        height: 205,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: ColoresApp.acento, width: 1),
            color: Colors.grey[200]),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 150,
                  height: 155,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16)),
                  ),
                  padding: const EdgeInsets.only(
                      top: 8, bottom: 2, left: 4, right: 4),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      GestureDetector(
                        onTap: () {
                          context.push(
                              "/tienda/${tiendaDestacada.codigo}");
                        },

                        child: Container(
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Color(0xFF8EB9FE), // Color principal
                                Color(0xFFC13584), // Color rosa de Instagram
                                Color(0xFFF56040), // Color naranja de Instagram
                              ],
                            ),
                          ),
                          padding: const EdgeInsets.all(3), // Ancho del borde
                          child: Container(
                            decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.white,
                                  width: 3,
                                ),
                              ),
                            child: Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: ColoresApp.acento,
                                  width: 2,
                                ),
                              ),
                              child: ClipOval(
                                child: Image.network(
                                  "${Ambiente.urlImagenes}/shoppings/${tiendaDestacada.imagen}",
                                  width: 70,
                                  height: 70,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 5),
                      Text(tiendaDestacada.nombre,
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                            'assets/images/bnb_reward_active.svg',
                            height: 18,
                          ),
                          const SizedBox(width: 4),
                          Text(tiendaDestacada.estampillas
                                .toString(),
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: const BorderRadius.only(
                            topRight: Radius.circular(15)),
                        child: Image.network(
                          "${Ambiente.urlImagenes}/rewards/${tiendaDestacada.imagenRecompensa}",
                          width: double.infinity,
                          height: 155,
                          fit: BoxFit.cover,
                        ),
                      ),
                      Positioned(
                        left: 0,
                        top: 0,
                        child: Container(
                          width: 10,
                          height: 155,
                          decoration: const BoxDecoration(
                              color: Colors.transparent),
                          child: ListView.builder(
                            physics:
                                const NeverScrollableScrollPhysics(),
                            itemCount: 20,
                            itemBuilder: (context, index) {
                              return SvgPicture.asset(
                                'assets/images/reward_destacado_ticket.svg',
                                width: 10,
                                height: 30,
                                fit: BoxFit.cover,
                              );
                            },
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 4,
                        right: 3,
                        child: ElevatedButton(
                          onPressed: () {
                            showDialog(
                                context: context,
                                builder: (context) {
                                  return TiendaInfoDialog(
                                    tiendaCodigo: tiendaDestacada.codigo,
                                  );
                                });
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: ColoresApp.amarillo,
                            shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 0),
                            minimumSize: const Size(0, 30),
                            tapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                            elevation: 0,
                          ),
                          child: const Text(
                            "Agregar tarjeta",
                            style: TextStyle(
                                fontSize: 10,
                                color: Colors.black,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
            Container(
              height: 48,
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(16),
                    bottomRight: Radius.circular(16)),
                border: Border(
                  top: BorderSide(
                    color: ColoresApp.fondo, // Color del borde
                    width: 2.0, // Grosor del borde
                  ),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    "Recompensa",
                    style: TextStyle(
                      fontSize: 10,
                      color: ColoresApp.texto,
                    ),
                  ),
                  const SizedBox(
                      height: 2), // Espaciado entre textos
                  Text(tiendaDestacada.recompensa,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                        fontSize: 14, // Tamaño normal
                        color: ColoresApp.acento,
                        fontWeight:
                            FontWeight.bold // Color por defecto
                        ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
