import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:myrewards/componentes/dialog_confirmacion.dart';
import 'package:myrewards/dtos/tienda_punto.dart';
import 'package:myrewards/estados/state_tiendas_destacadas.dart';
import 'package:myrewards/servicios/service_tienda.dart';
import 'package:myrewards/servicios/service_usuario.dart';
import 'package:provider/provider.dart';

class MapaScreen extends StatefulWidget {
  const MapaScreen({super.key});

  @override
  State<MapaScreen> createState() => _MapaScreenState();
}

class _MapaScreenState extends State<MapaScreen> {
  GoogleMapController? _controller;
  CameraPosition? deviceLocation;
  Position? userPosition;
  MarkerId? markerUbicacion;
  BitmapDescriptor markerIcon = BitmapDescriptor.defaultMarker;
  Map<MarkerId, Marker> markers = <MarkerId, Marker>{};

  loadMarkerIcon() async {
    final Uint8List markerList =
        await getBytesFromAsset('assets/images/marker_map.png', 95);
    final iconMarkers = BitmapDescriptor.fromBytes(markerList);
    setState(() {
      markerIcon = iconMarkers;
    });
  }

  Future<Uint8List> getBytesFromAsset(String path, int width) async {
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: width);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  getMarkers() async {
    if (mounted) {
      List<TiendaPunto> puntos = await TiendaApi().tiendasActivas();
      for (var punto in puntos) {
        MarkerId markerId = MarkerId(punto.codigo);

        Marker marker = Marker(
            markerId: markerId,
            infoWindow: InfoWindow(title: punto.nombre),
            position: LatLng(punto.latitud, punto.longitud),
            icon: markerIcon);

        markers[markerId] = marker;
      }

      setState(() {});
    }
  }

  @override
  void initState() {
    super.initState();
    loadMarkerIcon();
    validarPermisosUbicacion().then((value) => getMarkers());
  }

  Future getCurrentLocation() async {
    userPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high);

    deviceLocation = CameraPosition(
        target: LatLng(userPosition!.latitude, userPosition!.longitude),
        zoom: 15);

    UsuarioApi()
        .guardarUbicacion(userPosition!.latitude, userPosition!.longitude);

    buscarDestacados(userPosition!.latitude, userPosition!.longitude);

    setState(() {});
  }

  void createUserLocation() {
    markerUbicacion = const MarkerId("Ubicación");

    Marker marker = Marker(
        markerId: markerUbicacion!,
        position: LatLng(userPosition!.latitude, userPosition!.longitude));
    markers[markerUbicacion!] = marker;

    moverCamara(LatLng(userPosition!.latitude, userPosition!.longitude));

    // setState(() {});
  }

  Future moverCamara(LatLng newPosition) async {
    var zoomLevel = await _controller!.getZoomLevel();
    final CameraPosition newLocation = CameraPosition(
      target: newPosition,
      zoom: zoomLevel,
    );
    _controller!.animateCamera(CameraUpdate.newCameraPosition(newLocation));

    setState(() {});
  }

  Future validarPermisosUbicacion() async {
    LocationPermission permission = await Geolocator.checkPermission();

    if (permission == LocationPermission.whileInUse ||
        permission == LocationPermission.always) {
      getCurrentLocation();
    }

    if (permission == LocationPermission.denied ||
        permission == LocationPermission.unableToDetermine) {
      // Solicitar permiso al usuario
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.whileInUse ||
          permission == LocationPermission.always) {
        getCurrentLocation();
      } else {
        obtenerUbicacionSinPermiso();
      }
      return;
    }

    if (permission == LocationPermission.deniedForever) {
      mostrarDialogoConfiguracion();
      return;
    }

    bool isLocationEnabled = await Geolocator.isLocationServiceEnabled();
    if (!isLocationEnabled) {
      await Geolocator.openAppSettings();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: deviceLocation == null
            ? const Center(child: CircularProgressIndicator())
            : GoogleMap(
                mapType: MapType.normal,
                mapToolbarEnabled: false,
                zoomControlsEnabled: false,
                initialCameraPosition: deviceLocation!,
                onMapCreated: (GoogleMapController gmController) {
                  _controller = gmController;
                  createUserLocation();
                },
                markers: Set<Marker>.of(markers.values)));
  }

  void mostrarDialogoConfiguracion() {
    showDialog<String>(
        context: context,
        builder: (context) {
          return const ConfirmacionDialog(
              botonAceptar: "Habilitar",
              mensaje:
                  "Debes habilitar los permisos de ubicación en configuración para entregar mejores recompensas");
        }).then((value) {
      if (value == 'T') {
        Geolocator.openAppSettings();
      } else {
        obtenerUbicacionSinPermiso();
      }
    });
  }

  void obtenerUbicacionSinPermiso() {
    UsuarioApi().obtenerUbicacionPorIP().then((value) {
      var latitude = -33.015860;
      var longitude = -71.553480;

      if (value != null) {
        latitude = value["lat"];
        longitude = value["lon"];
      }

      deviceLocation =
          CameraPosition(target: LatLng(latitude, longitude), zoom: 15);

      buscarDestacados(latitude, longitude);
      setState(() {});
    });
  }

  void buscarDestacados(latitud, longitud) {
    context
        .read<TiendasDestacadasState>()
        .obtenerTiendasDestacadasGps(latitud, longitud);
  }
}
