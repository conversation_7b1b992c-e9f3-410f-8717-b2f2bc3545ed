import 'dart:convert';

import 'package:http/http.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:myrewards/constantes/ambiente.dart';
import 'package:myrewards/dtos/campacna.dart';
import 'package:myrewards/dtos/comunes/paginado.dart';
import 'package:myrewards/dtos/tienda_categoria.dart';
import 'package:myrewards/dtos/tienda_destacada.dart';
import 'package:myrewards/dtos/tienda_detalle.dart';
import 'package:myrewards/dtos/tienda_punto.dart';
import 'package:myrewards/dtos/tienda_red_social.dart';
import 'package:myrewards/http/auth_interceptor.dart';
import 'package:myrewards/http/retry_policy.dart';

class TiendaApi {
  Client client = InterceptedClient.build(
      interceptors: [LoggingInterceptor()],
      retryPolicy: ExpiredTokenRetryPolicy());

  static final TiendaApi _singleton = TiendaApi._internal();

  factory TiendaApi() {
    return _singleton;
  }

  TiendaApi._internal();

  Future<Campacna?> buscarCampacnaTienda(String codigoTienda) async {
    try {
      var response = await client
          .get(Uri.parse("${Ambiente.urlTienda}?code=$codigoTienda"));
      if (response.statusCode == 200) {
        return Campacna.fromJson(jsonDecode(utf8.decode(response.bodyBytes)));
      }
    } catch (e) {
      //controlar exception
    }

    return null;
  }

  Future<List<TiendaDestacada>> tiendasDestacadas() async {
    try {
      var response =
          await client.get(Uri.parse("${Ambiente.urlTienda}/featured/all"));
      if (response.statusCode == 200) {
        final Paginado data =
            Paginado.fromJson(jsonDecode(utf8.decode(response.bodyBytes)));
        List<TiendaDestacada> datos =
            data.content.map((e) => TiendaDestacada.fromJson(e)).toList();
        return datos;
      }
    } catch (e) {
      //controlar exception
    }

    return [];
  }

  Future<Paginado> tiendasDestacadasGps(latitud, longitud) async {
    try {
      var response = await client.get(Uri.parse(
          "${Ambiente.urlTienda}/featured/paged/$latitud/$longitud?page=0&size=10"));
      if (response.statusCode == 200) {
        return Paginado.fromJson(jsonDecode(utf8.decode(response.bodyBytes)));
      }
    } catch (e) {
      //controlar exception
    }

    return const Paginado(
        content: [],
        page: 0,
        size: 0,
        totalElements: 0,
        totalPages: 0,
        last: true);
  }

  Future<Paginado> tiendasDestacadasGpsPaginadas(
      latitud, longitud, page, size) async {
    try {
      var response = await client.get(Uri.parse(
          "${Ambiente.urlTienda}/featured/paged/$latitud/$longitud?page=$page&size=$size"));
      if (response.statusCode == 200) {
        return Paginado.fromJson(jsonDecode(utf8.decode(response.bodyBytes)));
      }
    } catch (e) {
      //controlar exception
    }

    return const Paginado(
        content: [],
        page: 0,
        size: 0,
        totalElements: 0,
        totalPages: 0,
        last: true);
  }

  Future<List<TiendaCategoria>> obtenerCategorias() async {
    try {
      var response =
          await client.get(Uri.parse("${Ambiente.urlTienda}/categories"));
      if (response.statusCode == 200) {
        final List data = jsonDecode(utf8.decode(response.bodyBytes));
        List<TiendaCategoria> datos =
            data.map((e) => TiendaCategoria.fromJson(e)).toList();
        datos.insert(
            0, const TiendaCategoria(codigo: "ALL", descripcion: "Todos"));
        return datos;
      }
    } catch (e) {
      //controlar exception
    }

    return [];
  }

  Future<List<TiendaPunto>> tiendasActivas() async {
    try {
      var response = await client.get(Uri.parse("${Ambiente.urlTienda}/map"));
      if (response.statusCode == 200) {
        final List data = jsonDecode(utf8.decode(response.bodyBytes));
        List<TiendaPunto> datos =
            data.map((e) => TiendaPunto.fromJson(e)).toList();
        return datos;
      }
    } catch (e) {
      //controlar exception
    }

    return [];
  }

  Future<TiendaDetalle?> tiendaDetalle(String codigo) async {
    try {
      var response =
          await client.get(Uri.parse("${Ambiente.urlTienda}/code/$codigo"));
      if (response.statusCode == 200) {
        return TiendaDetalle.fromJson(
            jsonDecode(utf8.decode(response.bodyBytes)));
      }

      return null;
    } catch (e) {
      throw Exception('Error inesperado, inténtalo en un momento');
    }
  }

  Future buscarUbicacionTienda(int tiendaId) async {
    try {
      var response = await client
          .get(Uri.parse("${Ambiente.urlTienda}/location/$tiendaId"));
      if (response.statusCode == 200) {
        return jsonDecode(utf8.decode(response.bodyBytes));
      }
    } catch (e) {
      //controlar exception
    }

    return null;
  }

  Future<List<TiendaRedSocial>> redesSociales(String codigoTienda) async {
    try {
      var response = await client.get(Uri.parse("${Ambiente.urlTienda}/social/code/$codigoTienda"));
      if (response.statusCode == 200) {
        final List data = jsonDecode(utf8.decode(response.bodyBytes));
        List<TiendaRedSocial> datos =
            data.map((e) => TiendaRedSocial.fromJson(e)).toList();
        return datos;
      }
    } catch (e) {
      //controlar exception
    }

    return [];
  }
}
