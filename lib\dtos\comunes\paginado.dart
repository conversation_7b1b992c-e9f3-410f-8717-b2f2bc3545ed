class Paginado {
  final List<dynamic> content;
  final int page;
  final int size;
  final int totalElements;
  final int totalPages;
  final bool last;

  const Paginado(
      {required this.content,
      required this.page,
      required this.size,
      required this.totalElements,
      required this.totalPages,
      required this.last});

  static Paginado fromJson(Map<String, dynamic> json) => Paginado(
      content: json['content'],
      page: json['page'],
      size: json['size'],
      totalElements: json['totalElements'],
      totalPages: json['totalPages'],
      last: json['last']);
}
