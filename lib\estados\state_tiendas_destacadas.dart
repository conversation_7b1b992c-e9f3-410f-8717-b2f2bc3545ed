import 'package:flutter/material.dart';
import 'package:myrewards/dtos/comunes/paginado.dart';
import 'package:myrewards/dtos/tienda_destacada.dart';
import 'package:myrewards/servicios/service_tienda.dart';

class TiendasDestacadasState with ChangeNotifier {
  List<TiendaDestacada> _tiendasDestacadas = [];
  var _latitud = -33.015860;
  var _longitud = -71.553480;
  bool _hasMoreData = false;

  bool _cargando = true;

  List<TiendaDestacada> get tiendasDestacadas => _tiendasDestacadas;
  bool get cargando => _cargando;
  bool get hasMoreData => _hasMoreData;

  double get latitud => _latitud;
  double get longitud => _longitud;

  obtenerTiendasDestacadas() async {
    _tiendasDestacadas = await TiendaApi().tiendasDestacadas();
    _cargando = false;
    notifyListeners();
  }

  obtenerTiendasDestacadasGps(latitudParam, longitudParam) async {
    _latitud = latitudParam;
    _longitud = longitudParam;
    Paginado pagina =
        await TiendaApi().tiendasDestacadasGps(_latitud, _longitud);
    _tiendasDestacadas =
        pagina.content.map((e) => TiendaDestacada.fromJson(e)).toList();
    _hasMoreData = !pagina.last;
    _cargando = false;
    notifyListeners();
  }

  actualizarDestacados() async {
    Paginado pagina =
        await TiendaApi().tiendasDestacadasGps(_latitud, _longitud);
    _tiendasDestacadas =
        pagina.content.map((e) => TiendaDestacada.fromJson(e)).toList();
    _hasMoreData = !pagina.last;
    notifyListeners();
  }
}
