import 'package:flutter/material.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:myrewards/dtos/recompensa_qr.dart';
import 'package:myrewards/estados/state_cupones.dart';
import 'package:myrewards/servicios/exception/api_exception.dart';
import 'package:myrewards/servicios/service_recompensa.dart';
import 'package:provider/provider.dart';

class RecompensaQrDialog extends StatefulWidget {
  final RecompensaQr recompensaQr;
  const RecompensaQrDialog({super.key, required this.recompensaQr});

  @override
  State<RecompensaQrDialog> createState() => _RecompensaQrDialogState();
}

class _RecompensaQrDialogState extends State<RecompensaQrDialog> {
  bool agregando = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      elevation: 0,
      backgroundColor: ColoresApp.fondo,
      child: <PERSON><PERSON>(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(height: 24),
                      Text(widget.recompensaQr.tiendaNombre,
                          style: const TextStyle(fontSize: 14)),
                      const SizedBox(height: 4),
                      Text(widget.recompensaQr.recompensaTitulo,
                          style: const TextStyle(
                              fontSize: 21,
                              fontWeight: FontWeight.bold,
                              color: ColoresApp.acento)),
                      const SizedBox(height: 8),
                      SizedBox(
                        height: 55,
                        width: double.infinity,
                        child: ElevatedButton(
                            onPressed: () {
                              if (agregando) return;
                              setState(() {
                                agregando = true;
                              });
                              RecompensasApi()
                                  .aceptarRecompensaPorCodigo(
                                      widget.recompensaQr.codigo)
                                  .then((_) {
                                setState(() {
                                  agregando = false;
                                });
                                context.read<CuponesState>().misCupones();

                                Navigator.of(context).pop("A");
                              }, onError: (e) {
                                if (mounted) {
                                  Navigator.of(context).pop();
                                  String errorMessage;

                                  if (e is ApiException) {
                                    errorMessage = e.message;
                                  } else {
                                    errorMessage = e.toString();
                                  }

                                  ScaffoldMessenger.of(context)
                                      .showSnackBar(SnackBar(
                                    content: Text(errorMessage),
                                  ));
                                }
                              });
                            },
                            style: ElevatedButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                            child: agregando
                                ? const Center(
                                    child: CircularProgressIndicator())
                                : const Text("Agregar",
                                    style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold))),
                      ),
                      const SizedBox(height: 8)
                    ],
                  ),
          ),
          Positioned(
            top: 0,
            right: 0,
            child: IconButton(
                iconSize: 30,
                icon: const Icon(
                  Icons.cancel,
                  color: Colors.grey,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                }),
          )
        ],
      ),
    );
  }
}
