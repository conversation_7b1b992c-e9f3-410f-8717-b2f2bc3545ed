import 'package:myrewards/constantes/ambiente.dart';

class Mensaje {
  String? usuarioLlave;
  String estado;
  final int id;
  final String? tiendaImg;
  final String? tienda;
  final int? tiendaId;
  final String? imagen;
  final DateTime fecha;
  final String titulo;
  final String mensaje;
  final String tipo;
  final String? data;

  Mensaje(
      {required this.id,
      required this.fecha,
      required this.titulo,
      required this.mensaje,
      required this.tipo,
      required this.estado,
      this.usuarioLlave,
      this.imagen,
      this.tiendaImg,
      this.tienda,
      this.tiendaId,
      this.data});

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'usuarioLlave': usuarioLlave,
      'fecha': fecha.toIso8601String(),
      'titulo': titulo,
      'mensaje': mensaje,
      'tipo': tipo,
      'estado': estado,
      'imagen': imagen,
      'tiendaImg': tiendaImg,
      'tienda': tienda,
      'tiendaId': tiendaId,
      'data': data
    };
  }

  factory Mensaje.fromMap(Map<String, dynamic> map) {
    return Mensaje(
        id: map['id'],
        tiendaImg: map['tiendaImg'],
        tienda: map['tienda'],
        tiendaId: map['tiendaId'],
        imagen: map['imagen'],
        titulo: map['titulo'],
        tipo: map['tipo'],
        mensaje: map['mensaje'],
        fecha: DateTime.parse(map['fecha']),
        estado: map['estado'],
        data: map['data']);
  }

  factory Mensaje.fromJson(Map<String, dynamic> json) {
    return Mensaje(
        id: json['userMessageId'],
        tiendaImg:
            '${Ambiente.urlImagenes}/shoppings/${json['shoppingImage'] ?? 'logo_generico.jpg'}',
        tienda: json['shoppingName'] ?? 'MyReward',
        tiendaId: json['shoppingId'],
        imagen: json['image'],
        titulo: json['title'],
        tipo: json['type'],
        mensaje: json['message'],
        fecha: DateTime.parse(json['date']),
        estado: json['status'] ?? 'SENT',
        data: json['data']);
  }
}
