import 'dart:async';
import 'dart:convert';

import 'package:http/http.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:myrewards/constantes/ambiente.dart';
import 'package:myrewards/dtos/recompensa_qr.dart';
import 'package:myrewards/http/auth_interceptor.dart';
import 'package:myrewards/http/retry_policy.dart';
import 'package:myrewards/servicios/exception/api_exception.dart';

class RecompensasApi {
  Client client = InterceptedClient.build(
      interceptors: [LoggingInterceptor()],
      retryPolicy: ExpiredTokenRetryPolicy());

  static final RecompensasApi _singleton = RecompensasApi._internal();

  factory RecompensasApi() {
    return _singleton;
  }

  RecompensasApi._internal();

  Future<RecompensaQr?> buscarRecompensaPorCodigo(String codigo) async {
    try {
      var response = await client.get(Uri.parse("${Ambiente.urlRecompensa}/code/$codigo"));
      if (response.statusCode == 200) {
        return RecompensaQr.fromJson(jsonDecode(utf8.decode(response.bodyBytes)));
      } else {
        final errorResponse = json.decode(response.body);
        throw ApiException.fromJson(errorResponse);
      }
    } on TimeoutException {
      throw TimeoutException(
          'La solicitud a tardó demasiado, inténtalo nuevamente más tarde.');
    } on ApiException {
      rethrow;
    } catch (e) {
      throw Exception('Error inesperado, inténtalo en un momento');
    }
  }

  Future<RecompensaQr?> aceptarRecompensaPorCodigo(String codigo) async {
    try {
      var response = await client.put(Uri.parse("${Ambiente.urlRecompensa}/code/$codigo"));
      if (response.statusCode == 200) {
        return RecompensaQr.fromJson(jsonDecode(utf8.decode(response.bodyBytes)));
      } else {
        final errorResponse = json.decode(response.body);
        throw ApiException.fromJson(errorResponse);
      }
    } on TimeoutException {
      throw TimeoutException(
          'La solicitud a tardó demasiado, inténtalo nuevamente más tarde.');
    } on ApiException {
      rethrow;
    } catch (e) {
      throw Exception('Error inesperado, inténtalo en un momento');
    }
  }
}
