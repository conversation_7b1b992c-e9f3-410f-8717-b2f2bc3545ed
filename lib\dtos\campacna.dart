class Campacna {
  final String nombreTienda;
  final String nombreCampacna;
  final DateTime recompensaValidaHasta;
  final int estampillasTarjeta;
  final String recompensaCondicionCorta;
  final String recompensaCondicionLarga;
  final String recompensaTitulo;

  const Campac<PERSON>(
      {required this.nombreCampacna,
      required this.nombreTienda,
      required this.recompensaValidaHasta,
      required this.estampillasTarjeta,
      required this.recompensaCondicionCorta,
      required this.recompensaCondicionLarga,
      required this.recompensaTitulo});

  static Campacna fromJson(Map<String, dynamic> json) => Campacna(
      nombreTienda: json['shoppingName'],
      nombreCampacna: json['campaignName'],
      recompensaValidaHasta: DateTime.parse(json['rewardEndDate']),
      estampillasTarjeta: json['cardHits'],
      recompensaCondicionCorta: json['rewardShortCondition'],
      recompensaCondicionLarga: json['rewardLargeCondition'],
      recompensaTitulo: json['rewardTitle']);
}
