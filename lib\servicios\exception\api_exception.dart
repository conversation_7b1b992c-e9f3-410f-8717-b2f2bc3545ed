class ApiException implements Exception {
  final String date;
  final String code;
  final String message;

  ApiException({
    required this.date,
    required this.code,
    required this.message,
  });

  factory ApiException.fromJson(Map<String, dynamic> json) {
    return ApiException(
      date: json['date'] as String,
      code: json['code'] as String,
      message: json['message'] as String,
    );
  }

  @override
  String toString() {
    return 'ApiException: $code - $message';
  }
}
