import 'package:flutter/material.dart';
import 'package:myrewards/dtos/tarjeta.dart';
import 'package:myrewards/servicios/service_tarjeta.dart';

class TarjetasState with ChangeNotifier {
  List<Tarjeta> _tarjetas = [];
  List<Tarjeta> _tarjetasOriginal = [];
  String _tarjetaCategoria = 'ALL';
  bool _cargando = true;

  set tarjetas(value) {
    _tarjetas = value;
    notifyListeners();
  }

  List<Tarjeta> get tarjetas => _tarjetas;
  bool get cargando => _cargando;

  agregarTarjeta(Tarjeta tarjeta) {
    _tarjetasOriginal.add(tarjeta);
    filtrar();
    notifyListeners();
  }

  quitarTarjeta(String codigo) {
    _tarjetas = _tarjetas.where((element) => element.codigo != codigo).toList();
    _tarjetasOriginal = _tarjetasOriginal.where((element) => element.codigo != codigo).toList();
    notifyListeners();
  }

  misTarjetas() async {
    _tarjetasOriginal = await TarjetaApi().misTarjetas();
    filtrar();
    _cargando = false;
    notifyListeners();
  }

  filtrarTarjetas(String categoria) {
    _tarjetaCategoria = categoria;
    filtrar();
    notifyListeners();
  }

  filtrar() {
    if (_tarjetaCategoria == 'ALL') {
      _tarjetas = _tarjetasOriginal;
    } else {
      _tarjetas = _tarjetasOriginal
          .where((element) => element.tiendaCategoria == _tarjetaCategoria)
          .toList();
    }
  }

  actualizarHits(String codigoTarjeta, int hitsUsuario) {
    Tarjeta tarjeta =
        _tarjetas.firstWhere((element) => element.codigo == codigoTarjeta);
    tarjeta.hitsUsuario = hitsUsuario;
    notifyListeners();
  }

  Tarjeta buscarTarjeta(String codigoTarjeta) {
    return _tarjetas.firstWhere((element) => element.codigo == codigoTarjeta);
  }
}
