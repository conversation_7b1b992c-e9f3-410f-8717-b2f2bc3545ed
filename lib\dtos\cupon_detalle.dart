class CuponDetalle {
  final String codigo;
  final String titulo;
  final String tiendaNombre;
  final String recompensaCondicionCorta;
  final String recompensaCondicionLarga;
  final String recompensaUso;
  final DateTime fechaVigencia;
  final String? nombreUsuario;

  const CuponDetalle(
      {required this.codigo,
      required this.titulo,
      required this.tiendaNombre,
      required this.recompensaCondicionCorta,
      required this.recompensaCondicionLarga,
      required this.recompensaUso,
      this.nombreUsuario,
      required this.fechaVigencia});

  static CuponDetalle fromJson(Map<String, dynamic> json) => CuponDetalle(
      codigo: json['code'],
      titulo: json['rewardTitle'],
      tiendaNombre: json['shoppingName'],
      recompensaUso: json['useCondition'],
      recompensaCondicionLarga: json['largeCondition'],
      recompensaCondicionCorta: json['shortCondition'],
      nombreUsuario: json['userName'],
      fechaVigencia: DateTime.parse(json['userRewardEndDate']));
}
