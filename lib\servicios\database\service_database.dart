import 'package:myrewards/dtos/mensaje.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseService {
  static final DatabaseService instance = DatabaseService._init();
  static Database? _database;

  DatabaseService._init();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB('myreward_v1.db');
    return _database!;
  }

  Future<Database> _initDB(String fileName) async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, fileName);

    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDB,
    );
  }

  Future<void> _createDB(Database db, int version) async {
    await db.execute('''
      CREATE TABLE mensajes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        usuarioLlave INTEGER,
        fecha TEXT,
        titulo TEXT,
        mensaje TEXT,
        tipo TEXT,
        estado TEXT,
        imagen TEXT,
        tiendaImg TEXT,
        tienda TEXT,
        tiendaId INTEGER,
        data TEXT
      )
    ''');
  }

  Future<void> insertMensaje(Mensaje mensaje) async {
    final db = await database;
    await db.insert(
      'mensajes',
      mensaje.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<Mensaje>> getMensajes(String usuarioLlave) async {
    final db = await database;
    final maps = await db.query('mensajes',
        where: 'usuarioLlave = ?',
        whereArgs: [usuarioLlave],
        orderBy: 'id DESC');

    return maps.map((map) => Mensaje.fromMap(map)).toList();
  }

  Future<int> contarNoLeidos() async {
    final db = await database;
    final total = await db.rawQuery(
        "SELECT COUNT(*) as total FROM mensajes WHERE estado != ?", ['VIEWED']);

    return Sqflite.firstIntValue(total) ?? 0;
  }

  Future<void> deleteAllMensajes() async {
    final db = await database;
    await db.delete('mensajes');
  }

  Future<void> deleteMensajeById(int id) async {
    final db = await database;
    await db.delete(
      'mensajes',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> actualizarEstado(int id, String nuevoEstado) async {
    final db = await database;

    await db.update(
      'mensajes',
      {'estado': nuevoEstado},
      where: 'id = ?',
      whereArgs: [id],
    );
  }
}
