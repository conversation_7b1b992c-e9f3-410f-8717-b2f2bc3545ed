import 'dart:io';
import 'package:flutter/material.dart';
import 'package:myrewards/componentes/dialog_alerta.dart';
import 'package:myrewards/componentes/dialog_teclado_codigo.dart';
import 'package:myrewards/componentes/dialog_tienda_info.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:myrewards/estados/state_cupones.dart';
import 'package:myrewards/pantallas/inicio/widgets/widget_recompoensa_animation.dart';
import 'package:myrewards/servicios/exception/api_exception.dart';
import 'package:myrewards/servicios/service_recompensa.dart';
//import 'package:qr_code_scanner_plus/qr_code_scanner.dart';
import 'package:qr_code_scanner_plus/qr_code_scanner_plus.dart';
import 'package:provider/provider.dart';

class LectorQrScreen extends StatefulWidget {
  const LectorQrScreen({super.key});

  @override
  State<LectorQrScreen> createState() => _LectorQrScreenState();
}

class _LectorQrScreenState extends State<LectorQrScreen> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  Barcode? result;
  QRViewController? controller;
  String? tiendaCodigo;

  @override
  void initState() {
    super.initState();
    tiendaCodigo = null;
  }

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) {
      if (scanData.code != null && tiendaCodigo == null) {
        Uri appUri = Uri.dataFromString(scanData.code!);
        tiendaCodigo = appUri.pathSegments.last;
        if (tiendaCodigo != null && tiendaCodigo!.startsWith("ST")) {
          buscarCampacnaPorCodigo(tiendaCodigo!);
        } else if (tiendaCodigo != null && tiendaCodigo!.startsWith("CR")) {
          buscarRecompensa(tiendaCodigo!);
        }
      }
    });
  }

  void showRewardAnimation(BuildContext context) {
    final overlay = Overlay.of(context);
    final renderBox =
        context.findRenderObject() as RenderBox?; // Usar operador null-safe

    if (renderBox == null) return; // Validación adicional

    // Guardar referencia al OverlayEntry para poder eliminarlo después
    final entry = OverlayEntry(
      builder: (context) => const RewardLottieAnimation(
        duration: Duration(seconds: 2),
      ),
    );

    overlay.insert(entry);

    // Eliminar el overlay después de que termine la animación
    Future.delayed(const Duration(seconds: 2), () {
      entry.remove();
    });
  }

  void buscarRecompensa(String recompensaCodigo) {
    RecompensasApi().aceptarRecompensaPorCodigo(recompensaCodigo).then((value) {
      context.read<CuponesState>().misCupones();
      context.read<CuponesState>().nuevoCupon = true;
      showDialog(
          context: context,
          builder: (context) {
            return AlertaDialog(
                mensaje:
                    "¡¡ Felicidades !!, te has ganado el siguiente cupón: ${value!.recompensaTitulo}");
          }).then((value) {
        Future.delayed(const Duration(milliseconds: 500)).then((value) {
          Navigator.of(context).pop();
        });

        Future.delayed(const Duration(milliseconds: 800)).then((value) {
          showRewardAnimation(context);
        });
      });
    }, onError: (e) {
      if (mounted) {
        Navigator.of(context).pop();
        String errorMessage;

        if (e is ApiException) {
          errorMessage = e.message;
        } else {
          errorMessage = e.toString();
        }

        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(errorMessage),
        ));
      }
    });
  }

  buscarCampacnaPorCodigo(String codigo) {
    showDialog<String?>(
        context: context,
        builder: (context) {
          return TiendaInfoDialog(tiendaCodigo: codigo);
        }).then((value) {
      if (value == "A") {
        Future.delayed(const Duration(milliseconds: 500))
            .then((value) => Navigator.of(context).pop());
      } else {
        tiendaCodigo = null;
      }
    });
  }

  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) {
      controller!.pauseCamera();
    } else if (Platform.isIOS) {
      controller!.resumeCamera();
    }
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text("Leer QR local",
              style: TextStyle(fontWeight: FontWeight.bold)),
          foregroundColor: ColoresApp.texto,
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
        body: Stack(children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(20)),
              child: QRView(
                key: qrKey,
                onQRViewCreated: _onQRViewCreated,
              ),
            ),
          ),
          Positioned(
              bottom: 16,
              right: 24,
              child: IconButton(
                icon: SvgPicture.asset(
                  'assets/images/keyboard_icon.svg',
                  height: 22,
                ),
                onPressed: () {
                  showDialog<String?>(
                      context: context,
                      builder: (context) {
                        return const TecladoCodigoDialog();
                      }).then((value) {
                    if (value != null && value.isNotEmpty) {
                      buscarCampacnaPorCodigo(value);
                    }
                  });
                },
              ))
        ]));
  }
}
