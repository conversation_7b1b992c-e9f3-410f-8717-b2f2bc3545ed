import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:myrewards/componentes/dialog_teclado_codigo.dart';
import 'package:myrewards/constantes/ambiente.dart';
import 'package:myrewards/constantes/colores.dart';
import 'package:go_router/go_router.dart';
import 'package:myrewards/estados/state_cupones.dart';
import 'package:myrewards/servicios/exception/api_exception.dart';
import 'package:myrewards/servicios/service_cupones.dart';
import 'package:myrewards/util/cargando_util.dart';
import 'package:provider/provider.dart';

class CuponesScreen extends StatefulWidget {
  const CuponesScreen({super.key});

  @override
  State<CuponesScreen> createState() => _CuponesScreenState();
}

class _CuponesScreenState extends State<CuponesScreen> {
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  Future<void> misCupones() async {
    context.read<CuponesState>().misCupones();
  }

  @override
  void initState() {
    super.initState();
    misCupones();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          foregroundColor: ColoresApp.texto,
          backgroundColor: ColoresApp.acento,
          elevation: 0,
          title: Row(
            children: [
              const Expanded(
                child: Text("Recompensas",
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        fontWeight: FontWeight.bold, color: Colors.white)),
              ),
              TextButton(
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.only(right: 16.0),
                  foregroundColor: Colors.white, // Color del texto
                  backgroundColor: Colors.transparent, // Sin fondo extra
                  tapTargetSize: MaterialTapTargetSize
                      .shrinkWrap, // Reduce el área de toque
                  minimumSize:
                      const Size(0, 0), // Evita el padding interno por defecto
                ),
                onPressed: () {
                  showDialog<String?>(
                    context: context,
                    builder: (context) => const TecladoCodigoDialog(),
                  ).then((value) {
                    if (value != null && value.isNotEmpty) {
                      buscarRecompensaPorCodigo(value);
                    }
                  });
                },
                child: const Text(
                  "¿Tienes un código?",
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              )
            ],
          )),
      body: context.watch<CuponesState>().cargando
          ? const AspectRatio(
              aspectRatio: 16 / 9,
              child: Center(child: CircularProgressIndicator()))
          : RefreshIndicator(
              key: _refreshIndicatorKey,
              onRefresh: misCupones,
              child: Padding(
                padding: const EdgeInsets.only(top: 8, left: 16, right: 16),
                child: !context.watch<CuponesState>().cargando &&
                        context.watch<CuponesState>().cupones.isEmpty
                    ? Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                            const SizedBox(height: 21),
                            SvgPicture.asset('assets/images/sin_cupones.svg'),
                            const SizedBox(height: 16),
                            const Text("Ouch ! Aún no tienes cupones",
                                style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: ColoresApp.acento)),
                            const SizedBox(height: 8),
                            const Text(
                                "No te desanimes, revisa todos los locales adheridos a promociones y recompensas",
                                style: TextStyle(color: ColoresApp.acento),
                                textAlign: TextAlign.center)
                          ])
                    : LayoutBuilder(
                        builder: (context, constraints) {
                          return GridView.builder(
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 2, // Dos columnas
                              childAspectRatio:
                                  0.55, // Ajusta el aspecto según tu diseño
                            ),
                            itemCount: context
                                .watch<CuponesState>()
                                .cupones
                                .length, // Número de tarjetas a mostrar
                            itemBuilder: (context, index) {
                              return GestureDetector(
                                onTap: () {
                                  context.push(
                                      "/cupon/${context.read<CuponesState>().cupones[index].cuponId}");
                                },
                                child: Card(
                                  color: ColoresApp.amarillo,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.stretch,
                                    children: [
                                      AspectRatio(
                                        aspectRatio: 1,
                                        child: ClipRRect(
                                          borderRadius:
                                              const BorderRadius.vertical(
                                                  top: Radius.circular(4.0)),
                                          child: Image.network(
                                            "${Ambiente.urlImagenes}/rewards/${context.read<CuponesState>().cupones[index].imagen}",
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                context
                                                    .read<CuponesState>()
                                                    .cupones[index]
                                                    .titulo,
                                                style: const TextStyle(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.bold,
                                                    color: ColoresApp.texto),
                                                textAlign: TextAlign.center,
                                              ),
                                              Container(
                                                decoration: BoxDecoration(
                                                  color: ColoresApp.segundario,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          21), // El radio por defecto de ElevatedButton
                                                ),
                                                alignment: Alignment.center,
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        vertical: 12,
                                                        horizontal: 16),
                                                child: const Text(
                                                  'Canjear',
                                                  style: TextStyle(
                                                    color: ColoresApp
                                                        .texto, // El texto por defecto de un ElevatedButton es blanco
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                              Align(
                                                alignment:
                                                    Alignment.bottomRight,
                                                child: Text(
                                                  "Válido hasta ${DateFormat('dd-MM-yyyy').format(context.read<CuponesState>().cupones[index].fechaVigencia)}",
                                                  style: const TextStyle(
                                                      fontSize: 10,
                                                      color: Colors.white),
                                                  textAlign: TextAlign.right,
                                                  // Evita el desbordamiento
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          );
                        },
                      ),
              ),
            ),
    );
  }

  buscarRecompensaPorCodigo(String value) {
    CargandoUtil().cargando("Buscando...", context);
    CuponesApi().agregarPorCodigo(value).then((valor) {
      if (mounted) {
        Navigator.of(context).pop();
        context.read<CuponesState>().misCupones();
      }
    }, onError: (e) {
      if (mounted) {
        Navigator.of(context).pop();
        String errorMessage;

        if (e is ApiException) {
          errorMessage = e.message;
        } else {
          errorMessage = e.toString();
        }

        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(errorMessage),
        ));
      }
    });
  }
}
