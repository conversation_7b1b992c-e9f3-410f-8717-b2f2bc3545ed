import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:myrewards/estados/state_cupones.dart';
import 'package:provider/provider.dart';

class AppBottomNav extends StatefulWidget {
  const AppBottomNav(
      {super.key, required this.indexChange, required this.initIndex});

  final Function indexChange;
  final int initIndex;

  @override
  State<AppBottomNav> createState() => _AppBottomNavState();
}

class _AppBottomNavState extends State<AppBottomNav> {
  int index = 0;

  @override
  void initState() {
    super.initState();
    index = widget.initIndex;
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20), topRight: Radius.circular(20)),
      child: BottomNavigationBar(
          backgroundColor: Colors.grey.shade100,
          currentIndex: index,
          elevation: 12,
          showSelectedLabels: true,
          showUnselectedLabels: true,
          selectedFontSize: 9,
          unselectedFontSize: 9,
          type: BottomNavigationBarType.fixed,
          onTap: (int i) {
            setState(() {
              index = i;
              widget.indexChange(index);
            });
          },
          items: [
            BottomNavigationBarItem(
                activeIcon: SvgPicture.asset(
                  'assets/images/bnb_home_active.svg',
                  height: 21,
                ),
                icon: SvgPicture.asset(
                  'assets/images/bnb_home_init.svg',
                  height: 21,
                ),
                label: "Inicio"),
            BottomNavigationBarItem(
                activeIcon: SvgPicture.asset(
                  'assets/images/bnb_map_active.svg',
                  height: 21,
                ),
                icon: SvgPicture.asset(
                  'assets/images/bnb_map_init.svg',
                  height: 21,
                ),
                label: "Mapa"),
            const BottomNavigationBarItem(icon: SizedBox.shrink(), label: ""),
            BottomNavigationBarItem(
                activeIcon: SvgPicture.asset(
                  'assets/images/bnb_reward_active.svg',
                  height: 21,
                ),
                icon: Stack(
                  children: [
                    SvgPicture.asset(
                      'assets/images/bnb_reward_init.svg',
                      height: 21,
                    ),
                    if (context.watch<CuponesState>().nuevoCupon)
                      const Positioned(
                          top: 0,
                          right: 0,
                          child: Icon(Icons.brightness_1,
                              size: 8.0, color: Colors.redAccent))
                  ],
                ),
                label: "Recompensas"),
            BottomNavigationBarItem(
                activeIcon: SvgPicture.asset(
                  'assets/images/bnb_user_active.svg',
                  height: 21,
                ),
                icon: SvgPicture.asset(
                  'assets/images/bnb_user_init.svg',
                  height: 21,
                ),
                label: "Mi cuenta"),
          ]),
    );
  }
}
