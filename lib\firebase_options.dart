// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBAuU2U48vjSSULyNNOo1ezlw6m9X8pge4',
    appId: '1:486775899310:android:327d86a12b8ae2454b993f',
    messagingSenderId: '486775899310',
    projectId: 'myreward-410713',
    storageBucket: 'myreward-410713.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyACgzMvM0LKVoyVFyYW-y5QFtO8uWJLKak',
    appId: '1:486775899310:ios:832dd5a7dec6ebef4b993f',
    messagingSenderId: '486775899310',
    projectId: 'myreward-410713',
    storageBucket: 'myreward-410713.appspot.com',
    androidClientId: '486775899310-273k2oti23gucq3jqt39lkklb5r19ts9.apps.googleusercontent.com',
    iosClientId: '486775899310-ppj6onjov73mjc82vr9t467bjt63jcmf.apps.googleusercontent.com',
    iosBundleId: 'cl.creabits.my.reward',
  );

}